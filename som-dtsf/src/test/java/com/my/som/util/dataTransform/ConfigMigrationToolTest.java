package com.my.som.util.dataTransform;

import org.junit.Before;
import org.junit.Test;
import org.junit.Rule;
import org.junit.rules.TemporaryFolder;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

import static org.junit.Assert.*;

/**
 * 配置迁移工具测试
 */
public class ConfigMigrationToolTest {

    private ConfigMigrationTool migrationTool;

    @Rule
    public TemporaryFolder tempFolder = new TemporaryFolder();

    @Before
    public void setUp() {
        migrationTool = new ConfigMigrationTool();
    }

    @Test
    public void testMigrateConfigurations() throws IOException {
        // 1. 创建旧版本转换配置文件
        String legacyConfig = createLegacyTransformConfig();
        File legacyConfigFile = tempFolder.newFile("legacy-transform-config.yml");
        Files.write(legacyConfigFile.toPath(), legacyConfig.getBytes());

        // 2. 创建现有路由配置文件
        String routeConfig = createExistingRouteConfig();
        File routeConfigFile = tempFolder.newFile("route-config.yml");
        Files.write(routeConfigFile.toPath(), routeConfig.getBytes());

        // 3. 创建转换配置目录
        File transformsDir = tempFolder.newFolder("transforms");

        // 4. 执行迁移
        ConfigMigrationTool.MigrationResult result = migrationTool.migrateConfigurations(
                legacyConfigFile.getAbsolutePath(),
                routeConfigFile.getAbsolutePath(),
                transformsDir.getAbsolutePath()
        );

        // 5. 验证迁移结果
        assertTrue("迁移应该成功", result.isSuccess());
        assertNotNull("迁移消息不应为空", result.getMessage());
        assertTrue("错误列表应该为空", result.getErrors().isEmpty());
        assertEquals("应该迁移2个配置", 2, result.getMigratedConfigs().size());

        // 6. 验证生成的配置文件
        assertTrue("应该生成patient-api-transform配置文件", 
                  Files.exists(Paths.get(transformsDir.getAbsolutePath(), "patient_api_transform-transform.yml")));
        assertTrue("应该生成medical-data-transform配置文件", 
                  Files.exists(Paths.get(transformsDir.getAbsolutePath(), "medical_data_transform-transform.yml")));

        // 7. 验证更新后的路由配置文件
        String updatedRouteConfig = new String(Files.readAllBytes(routeConfigFile.toPath()));
        assertTrue("路由配置应该包含transformConfig字段", updatedRouteConfig.contains("transformConfig"));
    }

    @Test
    public void testValidateMigration() throws IOException {
        // 1. 先执行迁移
        testMigrateConfigurations();

        // 2. 创建路由配置文件路径
        File routeConfigFile = new File(tempFolder.getRoot(), "route-config.yml");
        File transformsDir = new File(tempFolder.getRoot(), "transforms");

        // 3. 执行验证
        ConfigMigrationTool.ValidationReport report = migrationTool.validateMigration(
                routeConfigFile.getAbsolutePath(),
                transformsDir.getAbsolutePath()
        );

        // 4. 验证结果
        assertNotNull("验证报告不应为空", report);
        assertNotNull("验证消息不应为空", report.getMessage());
        
        // 注意：由于测试环境的限制，可能会有一些路径相关的错误，这是正常的
        // 主要验证工具能够正常运行
    }

    @Test
    public void testGenerateMigrationGuide() {
        String guide = migrationTool.generateMigrationGuide("config/transform-config.yml");
        
        assertNotNull("迁移指南不应为空", guide);
        assertTrue("指南应该包含标题", guide.contains("# 配置文件迁移指南"));
        assertTrue("指南应该包含概述", guide.contains("## 概述"));
        assertTrue("指南应该包含迁移步骤", guide.contains("## 迁移步骤"));
        assertTrue("指南应该包含新架构优势", guide.contains("## 新架构优势"));
        assertTrue("指南应该包含注意事项", guide.contains("## 注意事项"));
    }

    @Test
    public void testMigrationResultClass() {
        ConfigMigrationTool.MigrationResult result = new ConfigMigrationTool.MigrationResult();
        
        // 测试初始状态
        assertFalse("初始状态应该是失败", result.isSuccess());
        assertTrue("初始错误列表应该为空", result.getErrors().isEmpty());
        assertTrue("初始迁移配置映射应该为空", result.getMigratedConfigs().isEmpty());
        
        // 测试设置值
        result.setSuccess(true);
        result.setMessage("测试消息");
        result.addError("测试错误");
        result.addMigratedConfig("TEST_INFNO", "/path/to/config.yml");
        
        assertTrue("应该设置为成功", result.isSuccess());
        assertEquals("消息应该匹配", "测试消息", result.getMessage());
        assertEquals("应该有1个错误", 1, result.getErrors().size());
        assertEquals("应该有1个迁移配置", 1, result.getMigratedConfigs().size());
    }

    @Test
    public void testValidationReportClass() {
        ConfigMigrationTool.ValidationReport report = new ConfigMigrationTool.ValidationReport();
        
        // 测试初始状态
        assertFalse("初始状态应该是失败", report.isSuccess());
        assertTrue("初始错误列表应该为空", report.getErrors().isEmpty());
        assertTrue("初始警告列表应该为空", report.getWarnings().isEmpty());
        assertTrue("初始有效配置映射应该为空", report.getValidConfigs().isEmpty());
        
        // 测试设置值
        report.setSuccess(true);
        report.setMessage("验证消息");
        report.addError("验证错误");
        report.addWarning("验证警告");
        report.addValidConfig("TEST_INFNO", "/path/to/config.yml");
        
        assertTrue("应该设置为成功", report.isSuccess());
        assertEquals("消息应该匹配", "验证消息", report.getMessage());
        assertEquals("应该有1个错误", 1, report.getErrors().size());
        assertEquals("应该有1个警告", 1, report.getWarnings().size());
        assertEquals("应该有1个有效配置", 1, report.getValidConfigs().size());
    }

    // ==================== 辅助方法 ====================

    private String createLegacyTransformConfig() {
        return "transforms:\n" +
               "  - infno: \"PATIENT_API_TRANSFORM\"\n" +
               "    serviceType: \"apiTransform\"\n" +
               "    description: \"患者信息API双向转换\"\n" +
               "    version: \"1.0\"\n" +
               "    enabled: true\n" +
               "    \n" +
               "    responseTransform:\n" +
               "      enabled: true\n" +
               "      outputFormat: \"JSON\"\n" +
               "      transformMode: \"multiple\"\n" +
               "      \n" +
               "      targets:\n" +
               "        - targetName: \"StandardPatientInfo\"\n" +
               "          enabled: true\n" +
               "          outputFormat: \"JSON\"\n" +
               "          \n" +
               "          fieldMappings:\n" +
               "            - source: \"patient.id\"\n" +
               "              target: \"external_patient_id\"\n" +
               "              type: \"string\"\n" +
               "              required: true\n" +
               "\n" +
               "  - infno: \"MEDICAL_DATA_TRANSFORM\"\n" +
               "    serviceType: \"apiTransform\"\n" +
               "    description: \"医疗数据格式转换\"\n" +
               "    version: \"1.0\"\n" +
               "    enabled: true\n" +
               "    \n" +
               "    responseTransform:\n" +
               "      enabled: true\n" +
               "      outputFormat: \"JSON\"\n" +
               "      transformMode: \"single\"\n" +
               "      \n" +
               "      targets:\n" +
               "        - targetName: \"MedicalRecord\"\n" +
               "          enabled: true\n" +
               "          outputFormat: \"JSON\"\n" +
               "          \n" +
               "          fieldMappings:\n" +
               "            - source: \"record.id\"\n" +
               "              target: \"medical_record_id\"\n" +
               "              type: \"string\"\n" +
               "              required: true\n";
    }

    private String createExistingRouteConfig() {
        return "routes:\n" +
               "  - infno: \"HCM2203\"\n" +
               "    serviceType: \"dataExpose\"\n" +
               "    targetUrl: \"internal://dts/api/hcm2203\"\n" +
               "    method: \"POST\"\n" +
               "    targetFormat: \"JSON\"\n" +
               "    responseType: \"wrapped\"\n" +
               "    timeout: 15000\n" +
               "    headers:\n" +
               "      Content-Type: \"application/xml\"\n" +
               "\n" +
               "  - infno: \"SOM2301\"\n" +
               "    serviceType: \"dataExpose\"\n" +
               "    targetUrl: \"internal://dts/som2301\"\n" +
               "    method: \"POST\"\n" +
               "    targetFormat: \"JSON\"\n" +
               "    responseType: \"wrapped\"\n" +
               "    timeout: 15000\n" +
               "    headers:\n" +
               "      Content-Type: \"application/json\"\n";
    }
}