package com.my.som.controller.dataTransform;

import com.my.som.dto.dataTransform.*;
import com.my.som.service.dataTransform.ApiTransformService;
import com.my.som.service.dataTransform.MonitoringService;
import com.my.som.exception.dataTransform.TransformationException;
import com.my.som.exception.dataTransform.ConfigurationException;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * TransformController测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class TransformControllerTest {

    @Mock
    private ApiTransformService apiTransformService;

    @Mock
    private MonitoringService monitoringService;

    @Mock
    private BindingResult bindingResult;

    @InjectMocks
    private TransformController transformController;

    private TransformRequest validRequest;
    private TransformMetrics mockMetrics;

    @Before
    public void setUp() {
        // 创建有效的转换请求
        validRequest = new TransformRequest();
        validRequest.setInfno("1001");
        validRequest.setMsgid("MSG123456");
        validRequest.setMdtrtarea_admvs("440100");
        validRequest.setRecer_sys_code("SYS001");
        validRequest.setOpter("admin");
        validRequest.setOpter_name("管理员");
        validRequest.setInf_time("2024-12-11 10:30:00");
        validRequest.setFixmedins_code("H12345678901");
        validRequest.setFixmedins_name("测试医院");
        validRequest.setFixmedins_soft_fcty("测试软件厂商");
        validRequest.setInput("{\"patientId\":\"P123\",\"name\":\"张三\"}");

        // 创建模拟监控指标
        mockMetrics = new TransformMetrics();
        mockMetrics.setStartTime(System.currentTimeMillis());
    }

    @Test
    public void testProcessTransform_Success() {
        // 准备测试数据
        when(bindingResult.hasErrors()).thenReturn(false);
        when(monitoringService.startMonitoring(anyString(), anyString())).thenReturn(mockMetrics);

        TransformResult successResult = new TransformResult();
        successResult.setSuccess(true);
        successResult.setRequestId("TEST-REQ-123");
        Map<String, Object> resultData = new HashMap<>();
        resultData.put("result", "success");
        successResult.setTransformedData(resultData);
        successResult.setProcessingTime(100L);

        when(apiTransformService.transform(any(TransformRequest.class))).thenReturn(successResult);

        // 执行测试
        ResponseEntity<TransformResponse> response = transformController.processTransform(validRequest, bindingResult);

        // 验证结果
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("0", response.getBody().getInfcode());
        assertNotNull(response.getBody().getOutput());

        // 验证服务调用
        verify(monitoringService).startMonitoring(eq("1001"), anyString());
        verify(apiTransformService).transform(any(TransformRequest.class));
        verify(monitoringService).endMonitoring(any(TransformMetrics.class), eq(true), 
                                               isNull(), eq("1001"), anyString());
    }

    @Test
    public void testProcessTransform_ValidationError() {
        // 准备验证错误
        FieldError fieldError = new FieldError("transformRequest", "infno", "交易编号不能为空");
        when(bindingResult.hasErrors()).thenReturn(true);
        when(bindingResult.getFieldErrors()).thenReturn(Arrays.asList(fieldError));
        when(monitoringService.startMonitoring(anyString(), anyString())).thenReturn(mockMetrics);

        // 执行测试
        ResponseEntity<TransformResponse> response = transformController.processTransform(validRequest, bindingResult);

        // 验证结果
        assertNotNull(response);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("-1", response.getBody().getInfcode());
        assertTrue(response.getBody().getErr_msg().contains("交易编号不能为空"));

        // 验证服务未被调用
        verify(apiTransformService, never()).transform(any(TransformRequest.class));
    }

    @Test
    public void testProcessTransform_ConfigurationException() {
        // 准备测试数据
        when(bindingResult.hasErrors()).thenReturn(false);
        when(monitoringService.startMonitoring(anyString(), anyString())).thenReturn(mockMetrics);
        when(apiTransformService.transform(any(TransformRequest.class)))
                .thenThrow(new ConfigurationException("配置文件不存在"));

        // 执行测试
        ResponseEntity<TransformResponse> response = transformController.processTransform(validRequest, bindingResult);

        // 验证结果
        assertNotNull(response);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("-1", response.getBody().getInfcode());
        assertTrue(response.getBody().getErr_msg().contains("配置错误"));
    }

    @Test
    public void testProcessTransform_TransformationException() {
        // 准备测试数据
        when(bindingResult.hasErrors()).thenReturn(false);
        when(monitoringService.startMonitoring(anyString(), anyString())).thenReturn(mockMetrics);
        when(apiTransformService.transform(any(TransformRequest.class)))
                .thenThrow(new TransformationException(
                    com.my.som.exception.dataTransform.TransformationErrorType.CONFIGURATION_ERROR, 
                    "转换失败"));

        // 执行测试
        ResponseEntity<TransformResponse> response = transformController.processTransform(validRequest, bindingResult);

        // 验证结果
        assertNotNull(response);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("-1", response.getBody().getInfcode());
        assertTrue(response.getBody().getErr_msg().contains("转换异常"));
    }

    @Test
    public void testProcessTransform_SystemException() {
        // 准备测试数据
        when(bindingResult.hasErrors()).thenReturn(false);
        when(monitoringService.startMonitoring(anyString(), anyString())).thenReturn(mockMetrics);
        when(apiTransformService.transform(any(TransformRequest.class)))
                .thenThrow(new RuntimeException("系统异常"));

        // 执行测试
        ResponseEntity<TransformResponse> response = transformController.processTransform(validRequest, bindingResult);

        // 验证结果
        assertNotNull(response);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("-1", response.getBody().getInfcode());
        assertTrue(response.getBody().getErr_msg().contains("系统异常"));
    }

    @Test
    public void testGetStatistics_Success() {
        // 准备测试数据
        Map<String, Object> mockStatistics = new HashMap<>();
        mockStatistics.put("totalRequests", 100);
        mockStatistics.put("averageProcessingTime", 150.5);
        
        when(monitoringService.getPerformanceStatistics("1001")).thenReturn(mockStatistics);

        // 执行测试
        ResponseEntity<Map<String, Object>> response = transformController.getStatistics("1001");

        // 验证结果
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(100, response.getBody().get("totalRequests"));
        assertEquals(150.5, response.getBody().get("averageProcessingTime"));

        verify(monitoringService).getPerformanceStatistics("1001");
    }

    @Test
    public void testGetStatistics_Exception() {
        // 准备测试数据
        when(monitoringService.getPerformanceStatistics("1001"))
                .thenThrow(new RuntimeException("获取统计失败"));

        // 执行测试
        ResponseEntity<Map<String, Object>> response = transformController.getStatistics("1001");

        // 验证结果
        assertNotNull(response);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
    }

    @Test
    public void testGetErrorAnalysis_Success() {
        // 准备测试数据
        Map<String, Object> mockAnalysis = new HashMap<>();
        mockAnalysis.put("errorCount", 5);
        mockAnalysis.put("errorRate", 0.05);
        
        when(monitoringService.getErrorAnalysis("1001")).thenReturn(mockAnalysis);

        // 执行测试
        ResponseEntity<Map<String, Object>> response = transformController.getErrorAnalysis("1001");

        // 验证结果
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(5, response.getBody().get("errorCount"));
        assertEquals(0.05, response.getBody().get("errorRate"));

        verify(monitoringService).getErrorAnalysis("1001");
    }

    @Test
    public void testGenerateReport_Success() {
        // 准备测试数据
        Map<String, Object> mockReport = new HashMap<>();
        mockReport.put("reportId", "RPT-123");
        mockReport.put("generatedAt", "2024-12-11 10:30:00");
        
        when(monitoringService.generatePerformanceReport("1001")).thenReturn(mockReport);

        // 执行测试
        ResponseEntity<Map<String, Object>> response = transformController.generateReport("1001");

        // 验证结果
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("RPT-123", response.getBody().get("reportId"));

        verify(monitoringService).generatePerformanceReport("1001");
    }

    @Test
    public void testGetOptimizationSuggestions_Success() {
        // 准备测试数据
        List<String> mockSuggestions = Arrays.asList(
                "考虑启用缓存以提高性能",
                "优化字段映射配置以减少处理时间"
        );
        
        when(monitoringService.getOptimizationSuggestions("1001")).thenReturn(mockSuggestions);

        // 执行测试
        ResponseEntity<List<String>> response = transformController.getOptimizationSuggestions("1001");

        // 验证结果
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(2, response.getBody().size());
        assertTrue(response.getBody().contains("考虑启用缓存以提高性能"));

        verify(monitoringService).getOptimizationSuggestions("1001");
    }

    @Test
    public void testHandleException() {
        // 执行测试
        Exception testException = new RuntimeException("测试异常");
        ResponseEntity<TransformResponse> response = transformController.handleException(testException);

        // 验证结果
        assertNotNull(response);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("-1", response.getBody().getInfcode());
        assertTrue(response.getBody().getErr_msg().contains("系统内部错误"));
    }
}