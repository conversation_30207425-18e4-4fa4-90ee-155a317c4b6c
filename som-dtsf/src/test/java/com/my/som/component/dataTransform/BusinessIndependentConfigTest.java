package com.my.som.component.dataTransform;

import com.my.som.dto.dataTransform.RouteConfig;
import com.my.som.dto.dataTransform.RouteConfigContainer;
import com.my.som.dto.dataTransform.TransformConfig;
import com.my.som.dto.dataTransform.ValidationResult;
import org.junit.Before;
import org.junit.Test;
import org.junit.Rule;
import org.junit.rules.TemporaryFolder;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.Map;
import java.util.Set;

import static org.junit.Assert.*;

/**
 * 业务独立配置架构测试
 */
public class BusinessIndependentConfigTest {

    private TransformConfigLoader configLoader;

    @Rule
    public TemporaryFolder tempFolder = new TemporaryFolder();

    @Before
    public void setUp() {
        configLoader = new TransformConfigLoader();
        // 禁用热加载以避免测试中的异步问题
        ReflectionTestUtils.setField(configLoader, "hotReloadEnabled", false);
    }

    @Test
    public void testLoadBusinessIndependentConfigs() throws IOException {
        // 1. 创建路由配置文件
        String routeConfig = createRouteConfigContent();
        File routeConfigFile = tempFolder.newFile("route-config.yml");
        Files.write(routeConfigFile.toPath(), routeConfig.getBytes());
        
        // 2. 创建转换配置目录
        File transformsDir = tempFolder.newFolder("transforms");
        
        // 3. 创建业务独立转换配置文件
        String hcm2203Config = createHcm2203TransformConfig();
        File hcm2203ConfigFile = new File(transformsDir, "hcm2203-transform.yml");
        Files.write(hcm2203ConfigFile.toPath(), hcm2203Config.getBytes());
        
        String som2301Config = createSom2301TransformConfig();
        File som2301ConfigFile = new File(transformsDir, "som2301-transform.yml");
        Files.write(som2301ConfigFile.toPath(), som2301Config.getBytes());
        
        // 4. 设置配置文件路径
        ReflectionTestUtils.setField(configLoader, "routeConfigPath", routeConfigFile.getAbsolutePath());
        
        // 5. 初始化并加载配置
        configLoader.init();
        Map<String, TransformConfig> configs = configLoader.loadAllTransformConfigs();
        
        // 6. 验证加载结果
        assertNotNull("配置映射不应为空", configs);
        assertEquals("应该加载2个转换配置", 2, configs.size());
        
        // 验证HCM2203配置
        TransformConfig hcm2203Transform = configs.get("HCM2203");
        assertNotNull("HCM2203转换配置不应为空", hcm2203Transform);
        assertEquals("HCM2203", hcm2203Transform.getInfno());
        assertEquals("dataExpose", hcm2203Transform.getServiceType());
        
        // 验证SOM2301配置
        TransformConfig som2301Transform = configs.get("SOM2301");
        assertNotNull("SOM2301转换配置不应为空", som2301Transform);
        assertEquals("SOM2301", som2301Transform.getInfno());
        assertEquals("dataExpose", som2301Transform.getServiceType());
    }

    @Test
    public void testGetConfigByInfono() throws IOException {
        // 设置测试环境
        setupBusinessIndependentConfig();
        
        // 测试根据infno获取配置
        TransformConfig hcm2203Config = configLoader.getConfigByInfono("HCM2203");
        assertNotNull("应该能获取到HCM2203配置", hcm2203Config);
        assertEquals("HCM2203", hcm2203Config.getInfno());
        
        TransformConfig som2301Config = configLoader.getConfigByInfono("SOM2301");
        assertNotNull("应该能获取到SOM2301配置", som2301Config);
        assertEquals("SOM2301", som2301Config.getInfno());
        
        // 测试不存在的配置
        TransformConfig nonExistentConfig = configLoader.getConfigByInfono("NON_EXISTENT");
        assertNull("不存在的配置应该返回null", nonExistentConfig);
    }

    @Test
    public void testGetTransformConfigPath() throws IOException {
        // 设置测试环境
        setupBusinessIndependentConfig();
        
        // 测试获取转换配置路径
        String hcm2203Path = configLoader.getTransformConfigPath("HCM2203");
        assertNotNull("HCM2203转换配置路径不应为空", hcm2203Path);
        assertTrue("路径应该包含hcm2203-transform.yml", hcm2203Path.contains("hcm2203-transform.yml"));
        
        String som2301Path = configLoader.getTransformConfigPath("SOM2301");
        assertNotNull("SOM2301转换配置路径不应为空", som2301Path);
        assertTrue("路径应该包含som2301-transform.yml", som2301Path.contains("som2301-transform.yml"));
        
        // 测试不存在的路由
        String nonExistentPath = configLoader.getTransformConfigPath("NON_EXISTENT");
        assertNull("不存在的路由应该返回null", nonExistentPath);
    }

    @Test
    public void testGetLoadedConfigPaths() throws IOException {
        // 设置测试环境
        setupBusinessIndependentConfig();
        
        // 测试获取已加载的配置路径
        Set<String> loadedPaths = configLoader.getLoadedConfigPaths();
        assertNotNull("已加载配置路径集合不应为空", loadedPaths);
        assertEquals("应该有2个已加载的配置路径", 2, loadedPaths.size());
        
        boolean hasHcm2203 = loadedPaths.stream().anyMatch(path -> path.contains("hcm2203-transform.yml"));
        boolean hasSom2301 = loadedPaths.stream().anyMatch(path -> path.contains("som2301-transform.yml"));
        
        assertTrue("应该包含HCM2203配置路径", hasHcm2203);
        assertTrue("应该包含SOM2301配置路径", hasSom2301);
    }

    @Test
    public void testValidateRouteConfig() {
        String validRouteConfig = createRouteConfigContent();
        ValidationResult result = configLoader.validateRouteConfig(validRouteConfig);
        
        assertNotNull("验证结果不应为空", result);
        assertTrue("有效的路由配置应该通过验证", result.isValid());
        assertTrue("错误列表应该为空", result.getErrors().isEmpty());
    }

    @Test
    public void testValidateTransformConfig() {
        String validTransformConfig = createHcm2203TransformConfig();
        ValidationResult result = configLoader.validateTransformConfig(validTransformConfig);
        
        assertNotNull("验证结果不应为空", result);
        assertTrue("有效的转换配置应该通过验证", result.isValid());
        assertTrue("错误列表应该为空", result.getErrors().isEmpty());
    }

    @Test
    public void testRouteConfigContainer() throws IOException {
        // 创建路由配置文件
        String routeConfig = createRouteConfigContent();
        File routeConfigFile = tempFolder.newFile("route-config.yml");
        Files.write(routeConfigFile.toPath(), routeConfig.getBytes());
        
        // 设置配置文件路径
        ReflectionTestUtils.setField(configLoader, "routeConfigPath", routeConfigFile.getAbsolutePath());
        
        // 初始化并加载配置
        configLoader.init();
        RouteConfigContainer container = configLoader.getRouteConfigContainer();
        
        // 验证路由配置容器
        assertNotNull("路由配置容器不应为空", container);
        assertTrue("应该有路由配置", container.hasRoutes());
        assertEquals("应该有2个路由配置", 2, container.getRouteCount());
        
        // 验证根据infno查找路由
        RouteConfig hcm2203Route = container.findRouteByInfono("HCM2203");
        assertNotNull("应该能找到HCM2203路由", hcm2203Route);
        assertEquals("HCM2203", hcm2203Route.getInfno());
        
        // 验证转换配置路径
        Set<String> transformPaths = container.getAllTransformConfigPaths();
        assertEquals("应该有2个转换配置路径", 2, transformPaths.size());
    }

    @Test
    public void testReloadConfig() throws IOException {
        // 设置测试环境
        setupBusinessIndependentConfig();
        
        // 验证初始配置
        TransformConfig initialConfig = configLoader.getConfigByInfono("HCM2203");
        assertNotNull("初始配置不应为空", initialConfig);
        
        // 测试重新加载所有配置
        configLoader.reloadAllConfigs();
        
        // 验证重新加载后的配置
        TransformConfig reloadedConfig = configLoader.getConfigByInfono("HCM2203");
        assertNotNull("重新加载后的配置不应为空", reloadedConfig);
        assertEquals("配置infno应该保持一致", initialConfig.getInfno(), reloadedConfig.getInfno());
    }

    // ==================== 辅助方法 ====================

    private void setupBusinessIndependentConfig() throws IOException {
        // 创建路由配置文件
        String routeConfig = createRouteConfigContent();
        File routeConfigFile = tempFolder.newFile("route-config.yml");
        Files.write(routeConfigFile.toPath(), routeConfig.getBytes());
        
        // 创建转换配置目录
        File transformsDir = tempFolder.newFolder("transforms");
        
        // 创建业务独立转换配置文件
        String hcm2203Config = createHcm2203TransformConfig();
        File hcm2203ConfigFile = new File(transformsDir, "hcm2203-transform.yml");
        Files.write(hcm2203ConfigFile.toPath(), hcm2203Config.getBytes());
        
        String som2301Config = createSom2301TransformConfig();
        File som2301ConfigFile = new File(transformsDir, "som2301-transform.yml");
        Files.write(som2301ConfigFile.toPath(), som2301Config.getBytes());
        
        // 设置配置文件路径
        ReflectionTestUtils.setField(configLoader, "routeConfigPath", routeConfigFile.getAbsolutePath());
        
        // 初始化
        configLoader.init();
    }

    private String createRouteConfigContent() {
        File transformsDir = new File(tempFolder.getRoot(), "transforms");
        String hcm2203Path = new File(transformsDir, "hcm2203-transform.yml").getAbsolutePath();
        String som2301Path = new File(transformsDir, "som2301-transform.yml").getAbsolutePath();
        
        return "routes:\n" +
               "  - infno: \"HCM2203\"\n" +
               "    serviceType: \"dataExpose\"\n" +
               "    targetUrl: \"internal://dts/api/hcm2203\"\n" +
               "    method: \"POST\"\n" +
               "    targetFormat: \"JSON\"\n" +
               "    responseType: \"wrapped\"\n" +
               "    timeout: 15000\n" +
               "    transformConfig: \"" + hcm2203Path + "\"\n" +
               "    headers:\n" +
               "      Content-Type: \"application/xml\"\n" +
               "\n" +
               "  - infno: \"SOM2301\"\n" +
               "    serviceType: \"dataExpose\"\n" +
               "    targetUrl: \"internal://dts/som2301\"\n" +
               "    method: \"POST\"\n" +
               "    targetFormat: \"JSON\"\n" +
               "    responseType: \"wrapped\"\n" +
               "    timeout: 15000\n" +
               "    transformConfig: \"" + som2301Path + "\"\n" +
               "    headers:\n" +
               "      Content-Type: \"application/json\"\n";
    }

    private String createHcm2203TransformConfig() {
        return "infno: \"HCM2203\"\n" +
               "serviceType: \"dataExpose\"\n" +
               "description: \"HCM2203医疗数据转换\"\n" +
               "version: \"1.0\"\n" +
               "enabled: true\n" +
               "\n" +
               "responseTransform:\n" +
               "  enabled: true\n" +
               "  outputFormat: \"JSON\"\n" +
               "  transformMode: \"single\"\n" +
               "  \n" +
               "  targets:\n" +
               "    - targetName: \"HCM2203Response\"\n" +
               "      enabled: true\n" +
               "      outputFormat: \"JSON\"\n" +
               "      \n" +
               "      fieldMappings:\n" +
               "        - source: \"data.patientId\"\n" +
               "          target: \"patient_id\"\n" +
               "          type: \"string\"\n" +
               "          required: true\n" +
               "\n" +
               "caching:\n" +
               "  enabled: true\n" +
               "  ttl: 600\n" +
               "\n" +
               "monitoring:\n" +
               "  enabled: true\n" +
               "  logLevel: \"INFO\"\n";
    }

    private String createSom2301TransformConfig() {
        return "infno: \"SOM2301\"\n" +
               "serviceType: \"dataExpose\"\n" +
               "description: \"SOM2301业务数据转换\"\n" +
               "version: \"1.0\"\n" +
               "enabled: true\n" +
               "\n" +
               "responseTransform:\n" +
               "  enabled: true\n" +
               "  outputFormat: \"JSON\"\n" +
               "  transformMode: \"single\"\n" +
               "  \n" +
               "  targets:\n" +
               "    - targetName: \"SOM2301Response\"\n" +
               "      enabled: true\n" +
               "      outputFormat: \"JSON\"\n" +
               "      \n" +
               "      fieldMappings:\n" +
               "        - source: \"result.businessData\"\n" +
               "          target: \"business_info\"\n" +
               "          type: \"object\"\n" +
               "\n" +
               "caching:\n" +
               "  enabled: true\n" +
               "  ttl: 300\n" +
               "\n" +
               "monitoring:\n" +
               "  enabled: true\n" +
               "  logLevel: \"INFO\"\n";
    }
}