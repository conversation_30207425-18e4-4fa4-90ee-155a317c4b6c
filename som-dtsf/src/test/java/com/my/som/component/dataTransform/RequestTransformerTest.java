package com.my.som.component.dataTransform;

import com.my.som.component.dataTransform.transformer.TransformerRegistry;
import com.my.som.dto.dataTransform.*;
import com.my.som.util.dataTransform.TypeConverter;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * RequestTransformer单元测试
 */
@RunWith(MockitoJUnitRunner.class)
public class RequestTransformerTest {

    @Mock
    private FieldMapper fieldMapper;

    @Mock
    private TransformerRegistry transformerRegistry;

    @Mock
    private RequestValidator requestValidator;

    @Mock
    private TypeConverter typeConverter;

    @InjectMocks
    private RequestTransformer requestTransformer;

    private RequestTransformConfig config;
    private Map<String, Object> sourceData;

    @Before
    public void setUp() {
        config = new RequestTransformConfig();
        config.setEnabled(true);
        config.setInputFormat("JSON");

        sourceData = new HashMap<>();
        sourceData.put("patientId", "P12345");
        sourceData.put("patientName", "张三");
        sourceData.put("birthDate", "1990-05-15");
    }

    @Test
    public void testTransformRequest_Disabled() {
        // Given
        config.setEnabled(false);

        // When
        Object result = requestTransformer.transformRequest(sourceData, config);

        // Then
        assertEquals(sourceData, result);
        verifyZeroInteractions(fieldMapper);
        verifyZeroInteractions(requestValidator);
    }

    @Test
    public void testTransformRequest_NullConfig() {
        // When
        Object result = requestTransformer.transformRequest(sourceData, null);

        // Then
        assertEquals(sourceData, result);
        verifyZeroInteractions(fieldMapper);
        verifyZeroInteractions(requestValidator);
    }

    @Test
    public void testTransformRequest_WithFieldMappings() {
        // Given
        List<FieldMapping> fieldMappings = Arrays.asList(
            createFieldMapping("patientId", "external_patient_id", "string"),
            createFieldMapping("patientName", "patient_name", "string")
        );
        config.setFieldMappings(fieldMappings);

        Map<String, Object> mappedData = new HashMap<>();
        mappedData.put("external_patient_id", "P12345");
        mappedData.put("patient_name", "张三");

        when(fieldMapper.mapFields(sourceData, fieldMappings)).thenReturn(mappedData);

        // When
        Object result = requestTransformer.transformRequest(sourceData, config);

        // Then
        assertEquals(mappedData, result);
        verify(fieldMapper).mapFields(sourceData, fieldMappings);
    }

    @Test
    public void testPrepareApiCall_Success() {
        // Given
        TargetApiConfig targetApi = new TargetApiConfig();
        targetApi.setUrl("http://localhost:8080/api/patient");
        targetApi.setMethod("POST");
        targetApi.setTimeout(5000);
        config.setTargetApi(targetApi);

        // Set up field mappings to trigger the mapping
        List<FieldMapping> fieldMappings = Arrays.asList(
            createFieldMapping("patientId", "external_patient_id", "string")
        );
        config.setFieldMappings(fieldMappings);

        Map<String, Object> transformedData = new HashMap<>();
        transformedData.put("external_patient_id", "P12345");

        when(fieldMapper.mapFields(sourceData, fieldMappings)).thenReturn(transformedData);

        // When
        RequestTransformer.ApiCallPreparationResult result = 
            requestTransformer.prepareApiCall(sourceData, config);

        // Then
        assertTrue(result.isSuccess());
        assertEquals(transformedData, result.getTransformedData());
        assertEquals("http://localhost:8080/api/patient", result.getTargetUrl());
        assertEquals("POST", result.getMethod());
        assertEquals(5000, result.getTimeout());
        assertNull(result.getError());
    }

    private FieldMapping createFieldMapping(String source, String target, String type) {
        FieldMapping mapping = new FieldMapping();
        mapping.setSource(source);
        mapping.setTarget(target);
        mapping.setType(type);
        return mapping;
    }
}