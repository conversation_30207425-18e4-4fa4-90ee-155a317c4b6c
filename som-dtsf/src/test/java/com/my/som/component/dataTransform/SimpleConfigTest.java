package com.my.som.component.dataTransform;

import com.my.som.dto.dataTransform.ValidationResult;
import org.junit.Before;
import org.junit.Test;

import static org.junit.Assert.*;

/**
 * 简单配置测试
 */
public class SimpleConfigTest {

    private TransformConfigLoader configLoader;

    @Before
    public void setUp() {
        configLoader = new TransformConfigLoader();
    }

    @Test
    public void testValidateSimpleRouteConfig() {
        String validRouteConfig = "routes:\n" +
                                 "  - infno: \"TEST\"\n" +
                                 "    serviceType: \"dataExpose\"\n" +
                                 "    targetUrl: \"http://test.com\"\n" +
                                 "    method: \"POST\"\n" +
                                 "    timeout: 15000\n";
        
        ValidationResult result = configLoader.validateRouteConfig(validRouteConfig);
        
        assertNotNull("验证结果不应为空", result);
        if (!result.isValid()) {
            System.out.println("验证错误: " + result.getErrors());
            System.out.println("验证警告: " + result.getWarnings());
        }
        assertTrue("有效的路由配置应该通过验证", result.isValid());
    }

    @Test
    public void testValidateSimpleTransformConfig() {
        String validTransformConfig = "infno: \"TEST\"\n" +
                                     "serviceType: \"apiTransform\"\n" +
                                     "description: \"测试配置\"\n" +
                                     "enabled: true\n";
        
        ValidationResult result = configLoader.validateTransformConfig(validTransformConfig);
        
        assertNotNull("验证结果不应为空", result);
        if (!result.isValid()) {
            System.out.println("验证错误: " + result.getErrors());
            System.out.println("验证警告: " + result.getWarnings());
        }
        assertTrue("有效的转换配置应该通过验证", result.isValid());
    }
}