# 请求转换示例配置文件
# 文件路径: config/transforms/request-transform-example.yml

# 基础信息
infno: "REQUEST_TRANSFORM_EXAMPLE"
serviceType: "apiTransform"
description: "请求转换功能示例配置"
version: "1.0"
enabled: true

# 入参转换配置（新增功能）
requestTransform:
  enabled: true
  inputFormat: "JSON"
  
  # 参数验证配置
  validation:
    enabled: true
    requiredFields:
      - "patientId"
      - "patientName"
      - "requestType"
    
    fieldTypes:
      patientId: "string"
      patientName: "string"
      age: "integer"
      email: "string"
      requestType: "string"
    
    fieldFormats:
      email: "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$"
      patientId: "^P\\d{5}$"
    
    fieldRanges:
      age:
        min: 0
        max: 150
      patientName:
        min: 1
        max: 50
    
    customRules:
      - name: "validateRequestType"
        type: "expression"
        expression: "requestType in ['query', 'update', 'create']"
        errorMessage: "Request type must be one of: query, update, create"
  
  # 预处理器配置
  preprocessors:
    - "trimWhitespace"
    - "normalizeCase"
  
  # 字段映射配置
  fieldMappings:
    - source: "patientId"
      target: "external_patient_id"
      type: "string"
      required: true
      description: "患者外部ID映射"
      
    - source: "patientName"
      target: "patient_name"
      type: "string"
      required: true
      transformer: "normalizeCase"
      
    - source: "age"
      target: "patient_age"
      type: "integer"
      required: false
      defaultValue: 0
      
    - source: "email"
      target: "contact_email"
      type: "string"
      required: false
      transformer: "normalizeCase"
      
    - source: "requestType"
      target: "operation_type"
      type: "string"
      required: true
      transformer: "codeMapping"
      params:
        mappings:
          "query": "QUERY_PATIENT"
          "update": "UPDATE_PATIENT"
          "create": "CREATE_PATIENT"
        defaultValue: "QUERY_PATIENT"
  
  # 后处理器配置
  postprocessors:
    - "addTimestamp"
    - "addRequestId"
  
  # 目标API配置
  targetApi:
    url: "internal://api/patient/process"
    method: "POST"
    timeout: 10000
    retryCount: 2
    retryInterval: 1000
    headers:
      Content-Type: "application/json"
      X-Source-System: "DTSF"
      X-Transform-Version: "1.0"

# 出参转换配置
responseTransform:
  enabled: true
  outputFormat: "JSON"
  transformMode: "single"
  
  targets:
    - targetName: "StandardResponse"
      description: "标准响应格式"
      enabled: true
      outputFormat: "JSON"
      
      fieldMappings:
        - source: "result.patientInfo.id"
          target: "patient_id"
          type: "string"
          required: true
          
        - source: "result.patientInfo.name"
          target: "patient_name"
          type: "string"
          required: true
          
        - source: "result.patientInfo.status"
          target: "status"
          type: "string"
          transformer: "codeMapping"
          params:
            mappings:
              "ACTIVE": "活跃"
              "INACTIVE": "非活跃"
              "PENDING": "待处理"
            defaultValue: "未知"
        
        - source: "result.operationResult"
          target: "operation_result"
          type: "object"
          subMappings:
            - source: "success"
              target: "is_success"
              type: "boolean"
            - source: "message"
              target: "result_message"
              type: "string"

# 响应包装配置
responseWrapper:
  enabled: true
  wrapperType: "standard"
  templateEngine: "simple"
  successTemplate: |
    {
      "code": "0000",
      "message": "请求处理成功",
      "data": {{transformedData}},
      "timestamp": "{{currentTimestamp}}",
      "requestId": "{{requestId}}",
      "processingTime": "{{processingTime}}ms"
    }
  errorTemplate: |
    {
      "code": "{{errorCode}}",
      "message": "{{errorMessage}}",
      "data": null,
      "timestamp": "{{currentTimestamp}}",
      "requestId": "{{requestId}}",
      "error": {
        "type": "{{errorType}}",
        "details": "{{errorDetails}}"
      }
    }

# 缓存配置
caching:
  enabled: true
  ttl: 300
  cacheType: "memory"
  maxEntries: 1000
  keyFields: ["infno", "external_patient_id", "operation_type"]

# 监控配置
monitoring:
  enabled: true
  logLevel: "INFO"
  performanceThreshold: 2000
  enablePerformanceMonitoring: true
  enableDetailedLogging: true
  
  # 监控指标配置
  metrics:
    - name: "request_validation_time"
      description: "请求验证耗时"
      unit: "ms"
    - name: "field_mapping_time"
      description: "字段映射耗时"
      unit: "ms"
    - name: "api_call_time"
      description: "API调用耗时"
      unit: "ms"
    - name: "response_transform_time"
      description: "响应转换耗时"
      unit: "ms"