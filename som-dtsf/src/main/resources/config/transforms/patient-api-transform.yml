# 患者API业务转换配置文件
# 文件路径: config/transforms/patient-api-transform.yml

# 基础信息
infno: "PATIENT_API_TRANSFORM"
serviceType: "apiTransform"
description: "患者信息API双向转换"
version: "1.0"
enabled: true

# 出参转换配置（优先实现）
responseTransform:
  enabled: true
  outputFormat: "JSON"
  transformMode: "multiple"  # 支持多目标转换
  
  # 多目标转换配置
  targets:
    - targetName: "StandardPatientInfo"
      description: "标准患者信息格式"
      enabled: true
      priority: 1
      outputFormat: "JSON"
      
      # 条件配置：根据responseType决定是否生成此目标
      condition:
        field: "responseType"
        operator: "equals"
        value: "standard"
      
      # 字段映射配置
      fieldMappings:
        - source: "patient.id"
          target: "external_patient_id"
          type: "string"
          required: true
          description: "患者外部ID"
          
        - source: "patient.name"
          target: "patient_name"
          type: "string"
          required: true
          
        - source: "patient.birthDate"
          target: "birth_date"
          type: "string"
          transformer: "dateFormat"
          params:
            inputFormat: "yyyy-MM-dd"
            outputFormat: "yyyy/MM/dd"
        
        - source: "patient.gender"
          target: "gender_code"
          type: "string"
          transformer: "codeMapping"
          params:
            mappings:
              "男": "M"
              "女": "F"
              "未知": "U"
            defaultValue: "U"
    
    - targetName: "SimplePatientInfo"
      description: "简化患者信息格式"
      enabled: true
      priority: 2
      outputFormat: "JSON"
      
      # 条件配置
      condition:
        field: "responseType"
        operator: "equals"
        value: "simple"
      
      # 字段映射配置
      fieldMappings:
        - source: "patient.id"
          target: "id"
          type: "string"
          required: true
        - source: "patient.name"
          target: "name"
          type: "string"
          required: true

# 响应包装配置
responseWrapper:
  enabled: true
  wrapperType: "standard"
  templateEngine: "simple"
  successTemplate: |
    {
      "code": "0000",
      "message": "success",
      "data": {{transformedData}},
      "timestamp": "{{currentTimestamp}}",
      "requestId": "{{requestId}}"
    }
  errorTemplate: |
    {
      "code": "{{errorCode}}",
      "message": "{{errorMessage}}",
      "data": null,
      "timestamp": "{{currentTimestamp}}",
      "requestId": "{{requestId}}"
    }

# 入参转换配置（新增功能）
requestTransform:
  enabled: true
  inputFormat: "JSON"
  
  # 参数验证配置
  validation:
    enabled: true
    requiredFields:
      - "patientId"
      - "requestType"
    
    fieldTypes:
      patientId: "string"
      patientName: "string"
      requestType: "string"
    
    fieldFormats:
      patientId: "^P\\d{5}$"
    
    fieldRanges:
      patientName:
        min: 1
        max: 50
  
  # 字段映射配置
  fieldMappings:
    - source: "patientId"
      target: "external_patient_id"
      type: "string"
      required: true
      
    - source: "patientName"
      target: "patient_name"
      type: "string"
      required: false
      
    - source: "requestType"
      target: "operation_type"
      type: "string"
      required: true
      transformer: "codeMapping"
      params:
        mappings:
          "query": "QUERY_PATIENT"
          "update": "UPDATE_PATIENT"
        defaultValue: "QUERY_PATIENT"
  
  # 目标API配置
  targetApi:
    url: "internal://api/patient/info"
    method: "POST"
    timeout: 5000
    retryCount: 1
    retryInterval: 1000
    headers:
      Content-Type: "application/json"
      X-Source-System: "DTSF"

# 缓存配置
caching:
  enabled: true
  ttl: 300
  cacheType: "memory"
  maxEntries: 1000
  keyFields: ["infno", "responseType"]

# 监控配置
monitoring:
  enabled: true
  logLevel: "INFO"
  performanceThreshold: 1000
  enablePerformanceMonitoring: true