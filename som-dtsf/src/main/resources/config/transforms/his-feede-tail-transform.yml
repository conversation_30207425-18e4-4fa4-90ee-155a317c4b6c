# HIS费用明细业务转换配置文件
# 文件路径: config/transforms/his-feede-tail-transform.yml

infno: "getHisFeedeTail"
serviceType: "dataExpose"
description: "HIS费用明细数据转换"
version: "1.0"
enabled: true

responseTransform:
  enabled: true
  outputFormat: "JSON"
  transformMode: "single"
  
  targets:
    - targetName: "HisFeedeTailResponse"
      enabled: true
      outputFormat: "JSON"
      
      fieldMappings:
        - source: "feeDetails"
          target: "fee_details"
          type: "array"
          transformer: "arrayTransformer"
          subMappings:
            - source: "feeId"
              target: "fee_id"
              type: "string"
            - source: "feeType"
              target: "fee_type"
              type: "string"
            - source: "amount"
              target: "fee_amount"
              type: "number"
            - source: "feeDate"
              target: "fee_date"
              type: "string"
              transformer: "dateFormat"
              params:
                inputFormat: "yyyy-MM-dd HH:mm:ss"
                outputFormat: "yyyy-MM-dd"
        - source: "totalAmount"
          target: "total_amount"
          type: "number"
          required: true

responseWrapper:
  enabled: true
  successTemplate: |
    {
      "success": true,
      "code": "200",
      "message": "HIS费用明细查询成功",
      "data": {{transformedData}},
      "timestamp": "{{currentTimestamp}}"
    }

caching:
  enabled: true
  ttl: 300
  keyFields: ["infno", "patientId"]

monitoring:
  enabled: true
  logLevel: "INFO"
  performanceThreshold: 1000