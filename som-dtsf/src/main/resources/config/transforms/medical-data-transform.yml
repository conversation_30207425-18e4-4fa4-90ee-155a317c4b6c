# 医疗数据转换配置文件
# 文件路径: config/transforms/medical-data-transform.yml

infno: "MEDICAL_DATA_TRANSFORM"
serviceType: "apiTransform"
description: "医疗数据格式转换"
version: "1.0"
enabled: true

responseTransform:
  enabled: true
  outputFormat: "JSON"
  transformMode: "single"
  
  targets:
    - targetName: "MedicalRecord"
      enabled: true
      outputFormat: "JSON"
      
      fieldMappings:
        - source: "record.id"
          target: "medical_record_id"
          type: "string"
          required: true
        - source: "record.date"
          target: "record_date"
          type: "string"
          transformer: "dateFormat"
          params:
            inputFormat: "yyyy-MM-dd HH:mm:ss"
            outputFormat: "yyyy-MM-dd"
        - source: "record.diagnosis"
          target: "diagnosis_list"
          type: "array"
          transformer: "arrayTransformer"
          subMappings:
            - source: "code"
              target: "icd_code"
              type: "string"
            - source: "name"
              target: "diagnosis_name"
              type: "string"

caching:
  enabled: false

monitoring:
  enabled: true
  logLevel: "INFO"
  performanceThreshold: 500