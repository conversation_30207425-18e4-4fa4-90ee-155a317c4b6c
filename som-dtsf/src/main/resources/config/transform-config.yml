 数据转换配置文件
# 此配置文件定义了基于infno参数的数据转换规则
# 支持双向转换：入参转换和出参转换（优先实现出参转换）

transforms:
  # 患者信息API转换示例
  - infno: "PATIENT_API_TRANSFORM"
    serviceType: "apiTransform"
    description: "患者信息API双向转换"
    version: "1.0"
    enabled: true
    
    # 出参转换配置（优先实现）
    responseTransform:
      enabled: true
      outputFormat: "JSON"
      transformMode: "multiple"  # 支持多目标转换
      
      # 多目标转换配置
      targets:
        - targetName: "StandardPatientInfo"
          description: "标准患者信息格式"
          enabled: true
          priority: 1
          outputFormat: "JSON"
          
          # 条件配置：根据responseType决定是否生成此目标
          condition:
            field: "responseType"
            operator: "equals"
            value: "standard"
          
          # 字段映射配置
          fieldMappings:
            - source: "patient.id"
              target: "external_patient_id"
              type: "string"
              required: true
              description: "患者外部ID"
              
            - source: "patient.name"
              target: "patient_name"
              type: "string"
              required: true
              
            - source: "patient.birthDate"
              target: "birth_date"
              type: "string"
              transformer: "dateFormat"
              params:
                inputFormat: "yyyy-MM-dd"
                outputFormat: "yyyy/MM/dd"
              
            - source: "patient.gender"
              target: "gender_code"
              type: "string"
              transformer: "codeMapping"
              params:
                mappings:
                  "男": "M"
                  "女": "F"
                  "未知": "U"
                defaultValue: "U"
              
            - source: "patient.contacts"
              target: "contact_list"
              type: "array"
              transformer: "arrayTransformer"
              subMappings:
                - source: "type"
                  target: "contact_type"
                  type: "string"
                - source: "value"
                  target: "contact_value"
                  type: "string"
          
          # 验证配置
          validation:
            enabled: true
            requiredFields: ["external_patient_id", "patient_name"]
            fieldTypes:
              external_patient_id: "string"
              patient_name: "string"
              birth_date: "string"
              gender_code: "string"
            fieldLengths:
              external_patient_id: 50
              patient_name: 100
            regexPatterns:
              birth_date: "^\\d{4}/\\d{2}/\\d{2}$"
              gender_code: "^[MFU]$"
        
        - targetName: "SimplePatientInfo"
          description: "简化患者信息格式"
          enabled: true
          priority: 2
          outputFormat: "JSON"
          
          # 条件配置
          condition:
            field: "responseType"
            operator: "equals"
            value: "simple"
          
          # 字段映射配置
          fieldMappings:
            - source: "patient.id"
              target: "id"
              type: "string"
              required: true
            - source: "patient.name"
              target: "name"
              type: "string"
              required: true
            - source: "patient.age"
              target: "age"
              type: "integer"
              defaultValue: 0
      
      # 响应包装配置
      responseWrapper:
        enabled: true
        wrapperType: "standard"
        templateEngine: "simple"
        successTemplate: |
          {
            "code": "0000",
            "message": "success",
            "data": {{transformedData}},
            "timestamp": "{{currentTimestamp}}",
            "requestId": "{{requestId}}"
          }
        errorTemplate: |
          {
            "code": "{{errorCode}}",
            "message": "{{errorMessage}}",
            "data": null,
            "timestamp": "{{currentTimestamp}}",
            "requestId": "{{requestId}}"
          }
        templateVariables:
          systemName: "SOM-DTSF"
          version: "1.0"
      
      # 并行处理配置
      parallelProcessing:
        enabled: true
        threadPoolSize: 4
        maxWaitTime: 5000
        enableTimeout: true
        failureStrategy: "continue"
    
    # 入参转换配置（后续实现）
    requestTransform:
      enabled: false
      inputFormat: "JSON"
      # 配置结构待后续设计
    
    # 缓存配置
    caching:
      enabled: true
      ttl: 300
      cacheType: "memory"
      maxEntries: 1000
      keyFields: ["infno", "responseType"]
      evictionPolicy: "LRU"
      enableStatistics: true
      namespace: "patient_transform"
    
    # 监控配置
    monitoring:
      enabled: true
      logLevel: "INFO"
      performanceThreshold: 1000
      enablePerformanceMonitoring: true
      enableErrorMonitoring: true
      enableCacheMonitoring: true
      statisticsWindow: 300
      enableDetailedLogging: false
      dataRetentionHours: 24
      
      # 告警配置
      alerts:
        enabled: true
        rules:
          - name: "高延迟告警"
            metric: "processing_time"
            operator: "gt"
            threshold: 2000
            duration: 60
            severity: "warning"
          - name: "错误率告警"
            metric: "error_rate"
            operator: "gt"
            threshold: 0.1
            duration: 300
            severity: "critical"
        notificationChannels: ["email", "webhook"]

  # 医疗数据转换示例
  - infno: "MEDICAL_DATA_TRANSFORM"
    serviceType: "apiTransform"
    description: "医疗数据格式转换"
    version: "1.0"
    enabled: true
    
    responseTransform:
      enabled: true
      outputFormat: "JSON"
      transformMode: "single"
      
      targets:
        - targetName: "MedicalRecord"
          enabled: true
          outputFormat: "JSON"
          
          fieldMappings:
            - source: "record.id"
              target: "medical_record_id"
              type: "string"
              required: true
            - source: "record.date"
              target: "record_date"
              type: "string"
              transformer: "dateFormat"
              params:
                inputFormat: "yyyy-MM-dd HH:mm:ss"
                outputFormat: "yyyy-MM-dd"
            - source: "record.diagnosis"
              target: "diagnosis_list"
              type: "array"
              transformer: "arrayTransformer"
              subMappings:
                - source: "code"
                  target: "icd_code"
                  type: "string"
                - source: "name"
                  target: "diagnosis_name"
                  type: "string"
    
    caching:
      enabled: false
    
    monitoring:
      enabled: true
      logLevel: "INFO"
      performanceThreshold: 500

# 全局配置
global:
  # 默认配置
  defaults:
    caching:
      enabled: false
      ttl: 300
      cacheType: "memory"
    monitoring:
      enabled: true
      logLevel: "INFO"
      performanceThreshold: 1000
    responseTransform:
      outputFormat: "JSON"
      transformMode: "single"
  
  # 转换器注册
  transformers:
    dateFormat:
      className: "com.my.som.component.transformer.DateFormatTransformer"
      description: "日期格式转换器"
    codeMapping:
      className: "com.my.som.component.transformer.CodeMappingTransformer"
      description: "代码映射转换器"
    arrayTransformer:
      className: "com.my.som.component.transformer.ArrayTransformer"
      description: "数组转换器"
  
  # 系统配置
  system:
    configReloadInterval: 300  # 配置重载间隔（秒）
    enableHotReload: true      # 是否启用热重载
    maxConcurrentTransforms: 100  # 最大并发转换数
    defaultTimeout: 30000      # 默认超时时间（毫秒）