package com.my.som.exception.dataTransform;

/**
 * 转换错误类型枚举
 * 定义双向参数转换过程中可能出现的各种错误类型
 */
public enum TransformationErrorType {
    
    /**
     * 请求格式转换错误
     */
    REQUEST_FORMAT_ERROR("REQUEST_FORMAT_ERROR", "请求格式转换错误"),
    
    /**
     * 响应格式转换错误
     */
    RESPONSE_FORMAT_ERROR("RESPONSE_FORMAT_ERROR", "响应格式转换错误"),
    
    /**
     * 第三方接口调用错误
     */
    THIRD_PARTY_CALL_ERROR("THIRD_PARTY_CALL_ERROR", "第三方接口调用错误"),
    
    /**
     * 配置错误
     */
    CONFIGURATION_ERROR("CONFIGURATION_ERROR", "配置错误"),
    
    /**
     * 参数验证错误
     */
    VALIDATION_ERROR("VALIDATION_ERROR", "参数验证错误"),
    
    /**
     * 数据库连接失败
     */
    DATABASE_CONNECTION_FAILED("DATABASE_CONNECTION_FAILED", "数据库连接失败"),
    
    /**
     * 数据库驱动未找到
     */
    DATABASE_DRIVER_NOT_FOUND("DATABASE_DRIVER_NOT_FOUND", "数据库驱动未找到"),
    
    /**
     * SQL执行错误
     */
    SQL_EXECUTION_ERROR("SQL_EXECUTION_ERROR", "SQL执行错误"),
    
    /**
     * 不支持的数据库类型
     */
    UNSUPPORTED_DATABASE_TYPE("UNSUPPORTED_DATABASE_TYPE", "不支持的数据库类型"),
    
    /**
     * 数据库配置无效
     */
    DATABASE_CONFIG_INVALID("DATABASE_CONFIG_INVALID", "数据库配置无效"),
    
    /**
     * ResultSet转换失败
     */
    RESULTSET_CONVERSION_FAILED("RESULTSET_CONVERSION_FAILED", "ResultSet转换失败"),
    
    /**
     * ResultSet序列化失败
     */
    RESULTSET_SERIALIZATION_FAILED("RESULTSET_SERIALIZATION_FAILED", "ResultSet序列化失败"),
    
    /**
     * ResultSet反序列化失败
     */
    RESULTSET_DESERIALIZATION_FAILED("RESULTSET_DESERIALIZATION_FAILED", "ResultSet反序列化失败"),
    
    /**
     * ResultSet元数据无效
     */
    RESULTSET_METADATA_INVALID("RESULTSET_METADATA_INVALID", "ResultSet元数据无效"),
    
    /**
     * ResultSet传输失败
     */
    RESULTSET_TRANSMISSION_FAILED("RESULTSET_TRANSMISSION_FAILED", "ResultSet传输失败"),
    
    /**
     * 不支持的响应格式
     */
    UNSUPPORTED_RESPONSE_FORMAT("UNSUPPORTED_RESPONSE_FORMAT", "不支持的响应格式"),
    
    /**
     * 请求转换错误
     */
    REQUEST_TRANSFORM_ERROR("REQUEST_TRANSFORM_ERROR", "请求转换错误"),
    
    /**
     * 处理器执行错误
     */
    PROCESSOR_ERROR("PROCESSOR_ERROR", "处理器执行错误"),
    
    /**
     * 配置错误
     */
    CONFIG_ERROR("CONFIG_ERROR", "配置错误");
    
    private final String code;
    private final String description;
    
    TransformationErrorType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    @Override
    public String toString() {
        return code + ": " + description;
    }
}