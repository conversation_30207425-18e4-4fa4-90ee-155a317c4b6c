package com.my.som.service.dataTransform.impl;

import com.my.som.component.dataTransform.ResponseTransformer;
import com.my.som.component.dataTransform.TransformConfigLoader;
import com.my.som.component.dataTransform.FieldMapper;
import com.my.som.component.dataTransform.RequestTransformer;
import com.my.som.component.dataTransform.ApiCallService;
import com.my.som.dto.dataTransform.*;
import com.my.som.exception.dataTransform.ConfigurationException;
import com.my.som.exception.dataTransform.TransformationException;
import com.my.som.exception.dataTransform.TransformationErrorType;
import com.my.som.service.dataTransform.ApiTransformService;
import com.my.som.service.dataTransform.MonitoringService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * API数据转换服务实现
 * 作为转换服务的主入口，集成配置加载器、字段映射器和转换引擎
 */
@Service("apiTransformService")
public class ApiTransformServiceImpl implements ApiTransformService {

    private static final Logger logger = LoggerFactory.getLogger(ApiTransformServiceImpl.class);
    
    private static final String SERVICE_TYPE = "apiTransform";
    
    @Autowired
    private TransformConfigLoader configLoader;
    
    @Autowired
    private ResponseTransformer responseTransformer;
    
    @Autowired
    private FieldMapper fieldMapper;
    
    @Autowired
    private MonitoringService monitoringService;
    
    @Autowired
    private RequestTransformer requestTransformer;
    
    @Autowired
    private ApiCallService apiCallService;

    /**
     * 执行数据转换
     * 
     * @param request 转换请求对象
     * @return 转换结果对象
     */
    @Override
    public TransformResult transform(TransformRequest request) {
        String requestId = generateRequestId(request);
        String infno = request != null ? request.getInfno() : "unknown";
        
        // 开始监控
        TransformMetrics metrics = monitoringService.startMonitoring(infno, requestId);
        List<TransformError> errors = new ArrayList<>();
        boolean success = false;
        
        logger.info("开始处理转换请求，requestId: {}, infno: {}", requestId, infno);
        
        try {
            // 1. 参数验证
            long validationStart = System.currentTimeMillis();
            validateRequest(request);
            monitoringService.recordComponentPerformance(infno, "validation", 
                                                       System.currentTimeMillis() - validationStart);
            
            // 2. 根据infno匹配转换配置
            long configStart = System.currentTimeMillis();
            TransformConfig config = matchTransformConfig(request.getInfno());
            monitoringService.recordComponentPerformance(infno, "config_loading", 
                                                       System.currentTimeMillis() - configStart);
            
            if (config == null) {
                logger.warn("未找到匹配的转换配置，infno: {}", request.getInfno());
                TransformError error = createTransformError("CONFIG_NOT_FOUND", 
                                                          "未找到匹配的转换配置: " + request.getInfno(), 
                                                          null, null);
                errors.add(error);
                return createErrorResult(request, "未找到匹配的转换配置: " + request.getInfno(), errors);
            }
            
            logger.debug("找到匹配的转换配置，infno: {}, serviceType: {}", 
                        request.getInfno(), config.getServiceType());
            
            // 3. 解析输入数据
            long parseStart = System.currentTimeMillis();
            Object sourceData = parseInputData(request);
            monitoringService.recordComponentPerformance(infno, "data_parsing", 
                                                       System.currentTimeMillis() - parseStart);
            
            // 4. 执行转换
            long transformStart = System.currentTimeMillis();
            Object transformResult = executeTransform(sourceData, config, requestId);
            monitoringService.recordComponentPerformance(infno, "transformation", 
                                                       System.currentTimeMillis() - transformStart);
            
            // 5. 构建成功结果
            success = true;
            long processTime = System.currentTimeMillis() - metrics.getStartTime();
            return createSuccessResult(request, transformResult, processTime);
            
        } catch (ConfigurationException e) {
            logger.error("配置错误，requestId: {}, infno: {}", requestId, infno, e);
            TransformError error = createTransformError("CONFIGURATION_ERROR", e.getMessage(), null, e);
            errors.add(error);
            return createErrorResult(request, "配置错误: " + e.getMessage(), errors);
            
        } catch (TransformationException e) {
            logger.error("转换错误，requestId: {}, infno: {}", requestId, infno, e);
            TransformError error = createTransformError("TRANSFORMATION_ERROR", e.getMessage(), null, e);
            errors.add(error);
            return createErrorResult(request, "转换错误: " + e.getMessage(), errors);
            
        } catch (Exception e) {
            logger.error("系统错误，requestId: {}, infno: {}", requestId, infno, e);
            TransformError error = createTransformError("SYSTEM_ERROR", "系统内部错误: " + e.getMessage(), null, e);
            errors.add(error);
            return createErrorResult(request, "系统内部错误: " + e.getMessage(), errors);
            
        } finally {
            // 结束监控
            monitoringService.endMonitoring(metrics, success, errors, infno, requestId);
        }
    }

    /**
     * 获取服务类型
     * 
     * @return 服务类型标识
     */
    @Override
    public String getServiceType() {
        return SERVICE_TYPE;
    }

    /**
     * 验证转换请求
     * 
     * @param request 转换请求
     * @throws TransformationException 验证失败时抛出异常
     */
    private void validateRequest(TransformRequest request) throws TransformationException {
        if (request == null) {
            throw new TransformationException(TransformationErrorType.VALIDATION_ERROR, "转换请求不能为空");
        }
        
        if (!StringUtils.hasText(request.getInfno())) {
            throw new TransformationException(TransformationErrorType.VALIDATION_ERROR, "交易编号(infno)不能为空");
        }
        
        if (!StringUtils.hasText(request.getMsgid())) {
            throw new TransformationException(TransformationErrorType.VALIDATION_ERROR, "发送方报文ID(msgid)不能为空");
        }
        
        if (!StringUtils.hasText(request.getInput())) {
            throw new TransformationException(TransformationErrorType.VALIDATION_ERROR, "交易输入(input)不能为空");
        }
    }

    /**
     * 根据infno匹配转换配置
     * 
     * @param infno 交易编号
     * @return 匹配的转换配置，如果未找到返回null
     */
    private TransformConfig matchTransformConfig(String infno) {
        try {
            return configLoader.getConfigByInfono(infno);
        } catch (Exception e) {
            logger.error("获取转换配置失败，infno: {}", infno, e);
            throw new ConfigurationException("获取转换配置失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析输入数据
     * 
     * @param request 转换请求
     * @return 解析后的数据对象
     */
    private Object parseInputData(TransformRequest request) {
        try {
            String input = request.getInput();
            
            // 尝试解析为JSON
            if (input.trim().startsWith("{") || input.trim().startsWith("[")) {
                return com.alibaba.fastjson.JSON.parse(input);
            }
            
            // 如果不是JSON格式，直接返回字符串
            return input;
            
        } catch (Exception e) {
            logger.warn("解析输入数据失败，使用原始字符串，requestId: {}", 
                       generateRequestId(request), e);
            return request.getInput();
        }
    }

    /**
     * 执行转换
     * 
     * @param sourceData 源数据
     * @param config 转换配置
     * @param requestId 请求ID
     * @return 转换结果
     */
    private Object executeTransform(Object sourceData, TransformConfig config, String requestId) {
        Object processedData = sourceData;
        
        // 1. 执行请求转换（如果启用）
        RequestTransformConfig requestConfig = config.getRequestTransform();
        if (requestConfig != null && requestConfig.isEnabled()) {
            logger.debug("执行请求转换，requestId: {}", requestId);
            
            try {
                // 转换请求数据
                processedData = requestTransformer.transformRequest(sourceData, requestConfig);
                logger.debug("请求转换完成，requestId: {}", requestId);
                
                // 如果配置了目标API，执行API调用
                if (requestConfig.hasTargetApi()) {
                    logger.debug("执行目标API调用，requestId: {}", requestId);
                    
                    TargetApiConfig targetApi = requestConfig.getTargetApi();
                    // 解析内部URL
                    String resolvedUrl = apiCallService.resolveInternalUrl(targetApi.getUrl());
                    targetApi.setUrl(resolvedUrl);
                    
                    // 执行API调用
                    ApiCallService.ApiCallResult apiResult = apiCallService.callApiWithRetry(processedData, targetApi);
                    
                    if (apiResult.isSuccess()) {
                        processedData = apiResult.getResponseData();
                        logger.debug("API调用成功，requestId: {}", requestId);
                    } else {
                        logger.error("API调用失败，requestId: {}, error: {}", requestId, apiResult.getErrorMessage());
                        throw new TransformationException(
                            TransformationErrorType.THIRD_PARTY_CALL_ERROR,
                            "API调用失败: " + apiResult.getErrorMessage(),
                            apiResult.getException()
                        );
                    }
                }
                
            } catch (Exception e) {
                logger.error("请求转换失败，requestId: {}", requestId, e);
                throw new TransformationException(
                    TransformationErrorType.REQUEST_TRANSFORM_ERROR,
                    "请求转换失败: " + e.getMessage(),
                    e
                );
            }
        }
        
        // 2. 执行响应转换（如果启用）
        ResponseTransformConfig responseConfig = config.getResponseTransform();
        if (responseConfig == null || !responseConfig.isEnabled()) {
            logger.debug("响应转换未启用，返回处理后的数据，requestId: {}", requestId);
            return processedData;
        }
        
        // 执行完整的响应转换，包括包装和格式化
        String wrappedResponse = responseTransformer.transformAndWrapResponse(processedData, responseConfig, requestId);
        
        logger.debug("转换和包装完成，requestId: {}", requestId);
        
        return wrappedResponse;
    }

    /**
     * 创建成功结果
     * 
     * @param request 原始请求
     * @param transformResult 转换结果
     * @param processTime 处理时间
     * @return 成功结果对象
     */
    private TransformResult createSuccessResult(TransformRequest request, Object transformResult, long processTime) {
        TransformResult result = new TransformResult();
        result.setSuccess(true);
        result.setRequestId(generateRequestId(request));
        
        // 安全地设置转换结果
        if (transformResult instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> resultMap = (Map<String, Object>) transformResult;
            result.setTransformedData(resultMap);
        } else {
            // 如果不是Map类型，包装成Map
            Map<String, Object> wrappedResult = new HashMap<>();
            wrappedResult.put("result", transformResult);
            result.setTransformedData(wrappedResult);
        }
        
        result.setProcessingTime(processTime);
        result.setErrors(new ArrayList<>());
        
        // 创建指标
        TransformMetrics metrics = new TransformMetrics();
        metrics.setStartTime(System.currentTimeMillis() - processTime);
        metrics.setEndTime(System.currentTimeMillis());
        metrics.setTotalProcessingTime(processTime);
        result.setMetrics(metrics);
        
        logger.info("转换成功，requestId: {}, infno: {}, 处理时间: {}ms", 
                   result.getRequestId(), request.getInfno(), processTime);
        
        return result;
    }

    /**
     * 创建错误结果
     * 
     * @param request 原始请求
     * @param errorMessage 错误信息
     * @param errors 错误列表
     * @return 错误结果对象
     */
    private TransformResult createErrorResult(TransformRequest request, String errorMessage, List<TransformError> errors) {
        TransformResult result = new TransformResult();
        result.setSuccess(false);
        result.setRequestId(generateRequestId(request));
        result.setTransformedData(null);
        result.setProcessingTime(0);
        result.setErrors(errors != null ? errors : new ArrayList<>());
        
        // 如果错误列表为空，添加一个通用错误
        if (result.getErrors().isEmpty()) {
            TransformError error = createTransformError("GENERAL_ERROR", errorMessage, null, null);
            result.getErrors().add(error);
        }
        
        logger.error("转换失败，requestId: {}, infno: {}, 错误: {}", 
                    result.getRequestId(), 
                    request != null ? request.getInfno() : "unknown", 
                    errorMessage);
        
        return result;
    }

    /**
     * 生成请求ID
     * 
     * @param request 转换请求
     * @return 请求ID
     */
    private String generateRequestId(TransformRequest request) {
        if (request == null) {
            return "REQ-" + System.currentTimeMillis() + "-" + UUID.randomUUID().toString().substring(0, 8);
        }
        
        return String.format("REQ-%s-%s-%d", 
                           request.getInfno(), 
                           request.getMsgid(), 
                           System.currentTimeMillis());
    }
    
    /**
     * 创建转换错误对象
     * 
     * @param errorType 错误类型
     * @param errorMessage 错误消息
     * @param fieldPath 字段路径
     * @param cause 异常原因
     * @return 转换错误对象
     */
    private TransformError createTransformError(String errorType, String errorMessage, 
                                              String fieldPath, Exception cause) {
        TransformError error = new TransformError();
        error.setErrorCode(errorType);
        error.setErrorType(errorType);
        error.setErrorMessage(errorMessage);
        error.setFieldPath(fieldPath);
        error.setTimestamp(System.currentTimeMillis());
        
        if (cause != null) {
            error.setCause(cause);
        }
        
        return error;
    }
    
    /**
     * 验证转换配置
     * 
     * @param config 转换配置
     * @return 是否有效
     */
    @Override
    public boolean validateConfig(TransformConfig config) {
        if (config == null) {
            return false;
        }
        
        // 验证基本字段
        if (!StringUtils.hasText(config.getInfno())) {
            logger.warn("配置验证失败：infno为空");
            return false;
        }
        
        if (!StringUtils.hasText(config.getServiceType())) {
            logger.warn("配置验证失败：serviceType为空");
            return false;
        }
        
        // 验证响应转换配置
        ResponseTransformConfig responseConfig = config.getResponseTransform();
        if (responseConfig != null && responseConfig.isEnabled()) {
            if (responseConfig.getTargets() == null || responseConfig.getTargets().isEmpty()) {
                logger.warn("配置验证失败：响应转换已启用但目标配置为空");
                return false;
            }
        }
        
        logger.debug("配置验证通过：infno={}", config.getInfno());
        return true;
    }
    
    /**
     * 重新加载配置
     */
    @Override
    public void reloadConfig() {
        try {
            logger.info("开始重新加载转换配置");
            configLoader.reloadConfigs();
            logger.info("转换配置重新加载完成");
        } catch (Exception e) {
            logger.error("重新加载转换配置失败", e);
            throw new ConfigurationException("重新加载转换配置失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取支持的交易编号列表
     * 
     * @return 交易编号列表
     */
    @Override
    public List<String> getSupportedInfonos() {
        try {
            List<TransformConfig> configs = configLoader.loadConfigs();
            List<String> infonos = new ArrayList<>();
            
            for (TransformConfig config : configs) {
                if (StringUtils.hasText(config.getInfno())) {
                    infonos.add(config.getInfno());
                }
            }
            
            logger.debug("获取支持的交易编号列表，数量: {}", infonos.size());
            return infonos;
            
        } catch (Exception e) {
            logger.error("获取支持的交易编号列表失败", e);
            return new ArrayList<>();
        }
    }
}