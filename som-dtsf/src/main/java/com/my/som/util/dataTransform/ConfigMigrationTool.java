package com.my.som.util.dataTransform;

import com.my.som.dto.dataTransform.RouteConfig;
import com.my.som.dto.dataTransform.RouteConfigContainer;
import com.my.som.dto.dataTransform.TransformConfig;
import com.my.som.dto.dataTransform.TransformConfigContainer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.yaml.snakeyaml.DumperOptions;
import org.yaml.snakeyaml.Yaml;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 配置文件迁移工具
 * 自动将现有配置转换为新的业务独立架构
 */
@Component
public class ConfigMigrationTool {

    private static final Logger logger = LoggerFactory.getLogger(ConfigMigrationTool.class);

    private final Yaml yaml;

    public ConfigMigrationTool() {
        DumperOptions options = new DumperOptions();
        options.setDefaultFlowStyle(DumperOptions.FlowStyle.BLOCK);
        options.setPrettyFlow(true);
        options.setIndent(2);
        this.yaml = new Yaml(options);
    }

    /**
     * 执行配置迁移
     * 
     * @param legacyTransformConfigPath 旧版本转换配置文件路径
     * @param routeConfigPath 路由配置文件路径
     * @param transformsDir 业务独立转换配置文件目录
     * @return 迁移结果报告
     */
    public MigrationResult migrateConfigurations(String legacyTransformConfigPath, 
                                               String routeConfigPath, 
                                               String transformsDir) {
        MigrationResult result = new MigrationResult();
        
        try {
            logger.info("开始配置迁移：从 {} 到业务独立架构", legacyTransformConfigPath);
            
            // 1. 读取旧版本转换配置
            TransformConfigContainer legacyContainer = loadLegacyTransformConfig(legacyTransformConfigPath);
            if (legacyContainer == null || !legacyContainer.hasTransforms()) {
                result.addError("未找到有效的旧版本转换配置");
                return result;
            }
            
            // 2. 读取现有路由配置
            RouteConfigContainer routeContainer = loadRouteConfig(routeConfigPath);
            if (routeContainer == null) {
                // 创建新的路由配置容器
                routeContainer = new RouteConfigContainer();
                routeContainer.setRoutes(new ArrayList<>());
            }
            
            // 3. 创建转换配置目录
            createTransformsDirectory(transformsDir);
            
            // 4. 为每个转换配置创建业务独立文件
            List<TransformConfig> transforms = legacyContainer.getTransforms();
            for (TransformConfig transform : transforms) {
                try {
                    // 创建业务独立配置文件
                    String configFileName = generateConfigFileName(transform.getInfno());
                    String configFilePath = transformsDir + "/" + configFileName;
                    
                    // 写入转换配置文件
                    writeTransformConfig(transform, configFilePath);
                    
                    // 更新或创建对应的路由配置
                    updateRouteConfig(routeContainer, transform, "config/transforms/" + configFileName);
                    
                    result.addMigratedConfig(transform.getInfno(), configFilePath);
                    
                } catch (Exception e) {
                    logger.error("迁移转换配置失败：{}", transform.getInfno(), e);
                    result.addError("迁移转换配置失败：" + transform.getInfno() + " - " + e.getMessage());
                }
            }
            
            // 5. 写入更新后的路由配置
            writeRouteConfig(routeContainer, routeConfigPath);
            
            // 6. 备份旧配置文件
            backupLegacyConfig(legacyTransformConfigPath);
            
            result.setSuccess(true);
            result.setMessage("配置迁移完成，共迁移 " + result.getMigratedConfigs().size() + " 个配置");
            
            logger.info("配置迁移完成：共迁移 {} 个配置", result.getMigratedConfigs().size());
            
        } catch (Exception e) {
            logger.error("配置迁移失败", e);
            result.addError("配置迁移失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 验证迁移结果
     * 
     * @param routeConfigPath 路由配置文件路径
     * @param transformsDir 转换配置目录
     * @return 验证结果
     */
    public ValidationReport validateMigration(String routeConfigPath, String transformsDir) {
        ValidationReport report = new ValidationReport();
        
        try {
            logger.info("验证配置迁移结果");
            
            // 1. 验证路由配置文件
            RouteConfigContainer routeContainer = loadRouteConfig(routeConfigPath);
            if (routeContainer == null || !routeContainer.hasRoutes()) {
                report.addError("路由配置文件无效或为空");
                return report;
            }
            
            // 2. 验证每个路由的转换配置文件
            for (RouteConfig route : routeContainer.getRoutes()) {
                String transformConfigPath = route.getTransformConfig();
                if (transformConfigPath != null && !transformConfigPath.trim().isEmpty()) {
                    String fullPath = transformsDir + "/" + extractFileName(transformConfigPath);
                    
                    if (!Files.exists(Paths.get(fullPath))) {
                        report.addError("转换配置文件不存在：" + fullPath);
                    } else {
                        try {
                            // 尝试加载转换配置文件
                            TransformConfig config = loadTransformConfig(fullPath);
                            if (config == null) {
                                report.addError("转换配置文件格式错误：" + fullPath);
                            } else if (!route.getInfno().equals(config.getInfno())) {
                                report.addWarning("路由配置与转换配置的infno不匹配：" + 
                                                route.getInfno() + " vs " + config.getInfno());
                            } else {
                                report.addValidConfig(route.getInfno(), fullPath);
                            }
                        } catch (Exception e) {
                            report.addError("加载转换配置文件失败：" + fullPath + " - " + e.getMessage());
                        }
                    }
                }
            }
            
            report.setSuccess(report.getErrors().isEmpty());
            report.setMessage("验证完成：" + report.getValidConfigs().size() + " 个有效配置，" + 
                            report.getErrors().size() + " 个错误，" + 
                            report.getWarnings().size() + " 个警告");
            
            logger.info("配置迁移验证完成：{} 个有效配置，{} 个错误，{} 个警告", 
                       report.getValidConfigs().size(), report.getErrors().size(), report.getWarnings().size());
            
        } catch (Exception e) {
            logger.error("验证配置迁移失败", e);
            report.addError("验证失败：" + e.getMessage());
        }
        
        return report;
    }

    /**
     * 生成迁移指南
     * 
     * @param legacyTransformConfigPath 旧版本配置路径
     * @return 迁移指南文档
     */
    public String generateMigrationGuide(String legacyTransformConfigPath) {
        StringBuilder guide = new StringBuilder();
        
        guide.append("# 配置文件迁移指南\n\n");
        guide.append("## 概述\n\n");
        guide.append("本指南将帮助您将现有的统一转换配置文件迁移到新的业务独立配置架构。\n\n");
        
        guide.append("## 迁移步骤\n\n");
        guide.append("### 1. 备份现有配置\n");
        guide.append("```bash\n");
        guide.append("cp ").append(legacyTransformConfigPath).append(" ").append(legacyTransformConfigPath).append(".backup\n");
        guide.append("```\n\n");
        
        guide.append("### 2. 创建转换配置目录\n");
        guide.append("```bash\n");
        guide.append("mkdir -p src/main/resources/config/transforms/\n");
        guide.append("```\n\n");
        
        guide.append("### 3. 执行自动迁移\n");
        guide.append("使用ConfigMigrationTool.migrateConfigurations()方法执行自动迁移。\n\n");
        
        guide.append("### 4. 验证迁移结果\n");
        guide.append("使用ConfigMigrationTool.validateMigration()方法验证迁移结果。\n\n");
        
        guide.append("## 新架构优势\n\n");
        guide.append("- **业务隔离**：每个业务使用独立的转换配置文件\n");
        guide.append("- **配置清晰**：route-config.yml明确指定每个业务的转换配置文件\n");
        guide.append("- **维护便利**：业务配置变更只需修改对应的独立配置文件\n");
        guide.append("- **扩展性强**：新增业务只需创建新的转换配置文件\n\n");
        
        guide.append("## 注意事项\n\n");
        guide.append("1. 迁移后请测试所有业务功能\n");
        guide.append("2. 确保所有转换配置文件路径正确\n");
        guide.append("3. 保留备份文件以备回滚\n");
        
        return guide.toString();
    }

    // ==================== 私有方法 ====================

    private TransformConfigContainer loadLegacyTransformConfig(String configPath) throws IOException {
        if (!Files.exists(Paths.get(configPath))) {
            return null;
        }
        
        String content = new String(Files.readAllBytes(Paths.get(configPath)), "UTF-8");
        return yaml.load(content);
    }

    private RouteConfigContainer loadRouteConfig(String configPath) throws IOException {
        if (!Files.exists(Paths.get(configPath))) {
            return null;
        }
        
        String content = new String(Files.readAllBytes(Paths.get(configPath)), "UTF-8");
        return yaml.load(content);
    }

    private TransformConfig loadTransformConfig(String configPath) throws IOException {
        String content = new String(Files.readAllBytes(Paths.get(configPath)), "UTF-8");
        return yaml.load(content);
    }

    private void createTransformsDirectory(String transformsDir) throws IOException {
        Path dir = Paths.get(transformsDir);
        if (!Files.exists(dir)) {
            Files.createDirectories(dir);
            logger.info("创建转换配置目录：{}", transformsDir);
        }
    }

    private String generateConfigFileName(String infno) {
        return infno.toLowerCase().replace("_", "-") + "-transform.yml";
    }

    private void writeTransformConfig(TransformConfig config, String configPath) throws IOException {
        String yamlContent = yaml.dump(config);
        
        // 添加文件头注释
        StringBuilder content = new StringBuilder();
        content.append("# ").append(config.getDescription() != null ? config.getDescription() : "业务转换配置文件").append("\n");
        content.append("# 文件路径: ").append(configPath).append("\n");
        content.append("# 自动迁移生成时间: ").append(java.time.LocalDateTime.now()).append("\n\n");
        content.append(yamlContent);
        
        Files.write(Paths.get(configPath), content.toString().getBytes("UTF-8"));
        logger.info("写入转换配置文件：{}", configPath);
    }

    private void updateRouteConfig(RouteConfigContainer container, TransformConfig transform, String transformConfigPath) {
        // 查找是否已存在对应的路由配置
        RouteConfig existingRoute = container.findRouteByInfono(transform.getInfno());
        
        if (existingRoute != null) {
            // 更新现有路由配置
            existingRoute.setTransformConfig(transformConfigPath);
            logger.debug("更新路由配置：{}", transform.getInfno());
        } else {
            // 创建新的路由配置
            RouteConfig newRoute = new RouteConfig();
            newRoute.setInfno(transform.getInfno());
            newRoute.setServiceType(transform.getServiceType());
            newRoute.setTransformConfig(transformConfigPath);
            
            // 设置默认值
            newRoute.setTargetUrl("internal://api/" + transform.getInfno().toLowerCase());
            newRoute.setMethod("POST");
            newRoute.setTargetFormat("JSON");
            newRoute.setResponseType("wrapped");
            newRoute.setTimeout(15000);
            
            container.getRoutes().add(newRoute);
            logger.debug("创建新路由配置：{}", transform.getInfno());
        }
    }

    private void writeRouteConfig(RouteConfigContainer container, String configPath) throws IOException {
        String yamlContent = yaml.dump(container);
        
        // 添加文件头注释
        StringBuilder content = new StringBuilder();
        content.append("# 数据转换路由配置文件\n");
        content.append("# 此配置文件定义了根据infno参数值匹配的不同服务类型和处理方式\n");
        content.append("# 每个路由可以指定对应的业务独立转换配置文件\n");
        content.append("# 自动迁移更新时间: ").append(java.time.LocalDateTime.now()).append("\n\n");
        content.append(yamlContent);
        
        Files.write(Paths.get(configPath), content.toString().getBytes("UTF-8"));
        logger.info("写入路由配置文件：{}", configPath);
    }

    private void backupLegacyConfig(String configPath) throws IOException {
        Path source = Paths.get(configPath);
        Path backup = Paths.get(configPath + ".backup." + System.currentTimeMillis());
        
        if (Files.exists(source)) {
            Files.copy(source, backup);
            logger.info("备份旧配置文件：{} -> {}", configPath, backup);
        }
    }

    private String extractFileName(String path) {
        return Paths.get(path).getFileName().toString();
    }

    // ==================== 内部类 ====================

    /**
     * 迁移结果
     */
    public static class MigrationResult {
        private boolean success = false;
        private String message;
        private List<String> errors = new ArrayList<>();
        private Map<String, String> migratedConfigs = new HashMap<>();

        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public List<String> getErrors() {
            return errors;
        }

        public void addError(String error) {
            this.errors.add(error);
        }

        public Map<String, String> getMigratedConfigs() {
            return migratedConfigs;
        }

        public void addMigratedConfig(String infno, String configPath) {
            this.migratedConfigs.put(infno, configPath);
        }
    }

    /**
     * 验证报告
     */
    public static class ValidationReport {
        private boolean success = false;
        private String message;
        private List<String> errors = new ArrayList<>();
        private List<String> warnings = new ArrayList<>();
        private Map<String, String> validConfigs = new HashMap<>();

        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public List<String> getErrors() {
            return errors;
        }

        public void addError(String error) {
            this.errors.add(error);
        }

        public List<String> getWarnings() {
            return warnings;
        }

        public void addWarning(String warning) {
            this.warnings.add(warning);
        }

        public Map<String, String> getValidConfigs() {
            return validConfigs;
        }

        public void addValidConfig(String infno, String configPath) {
            this.validConfigs.put(infno, configPath);
        }
    }
}