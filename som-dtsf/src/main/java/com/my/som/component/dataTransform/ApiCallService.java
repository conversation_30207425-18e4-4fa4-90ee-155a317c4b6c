package com.my.som.component.dataTransform;

import com.my.som.dto.dataTransform.TargetApiConfig;
import com.my.som.exception.dataTransform.TransformationException;
import com.my.som.exception.dataTransform.TransformationErrorType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.ResourceAccessException;

import java.util.Map;

/**
 * API调用服务
 * 负责动态API路由和内部接口调用
 */
@Component
public class ApiCallService {

    private static final Logger logger = LoggerFactory.getLogger(ApiCallService.class);

    @Autowired
    private RestTemplate restTemplate;

    /**
     * 执行API调用
     *
     * @param requestData 请求数据
     * @param targetApi 目标API配置
     * @return API响应结果
     */
    public ApiCallResult callApi(Object requestData, TargetApiConfig targetApi) {
        if (targetApi == null || !StringUtils.hasText(targetApi.getUrl())) {
            throw new TransformationException(
                TransformationErrorType.CONFIG_ERROR,
                "Target API configuration is missing or invalid"
            );
        }

        long startTime = System.currentTimeMillis();
        String url = targetApi.getUrl();
        String method = targetApi.getMethod();
        
        logger.info("Calling API: {} {}", method, url);

        try {
            // 创建HTTP请求头
            HttpHeaders headers = createHttpHeaders(targetApi);
            
            // 创建HTTP实体
            HttpEntity<Object> entity = new HttpEntity<>(requestData, headers);
            
            // 执行API调用
            ResponseEntity<Object> response = executeApiCall(url, method, entity, targetApi);
            
            long processingTime = System.currentTimeMillis() - startTime;
            logger.info("API call completed successfully in {} ms, status: {}", 
                       processingTime, response.getStatusCode());

            return ApiCallResult.success(response.getBody(), response.getStatusCode(), processingTime);

        } catch (HttpClientErrorException e) {
            long processingTime = System.currentTimeMillis() - startTime;
            logger.error("API call failed with client error after {} ms: {} {}", 
                        processingTime, e.getStatusCode(), e.getMessage());
            
            return ApiCallResult.failure(
                "Client error: " + e.getStatusCode() + " - " + e.getMessage(),
                e.getStatusCode(),
                processingTime,
                e
            );

        } catch (HttpServerErrorException e) {
            long processingTime = System.currentTimeMillis() - startTime;
            logger.error("API call failed with server error after {} ms: {} {}", 
                        processingTime, e.getStatusCode(), e.getMessage());
            
            return ApiCallResult.failure(
                "Server error: " + e.getStatusCode() + " - " + e.getMessage(),
                e.getStatusCode(),
                processingTime,
                e
            );

        } catch (ResourceAccessException e) {
            long processingTime = System.currentTimeMillis() - startTime;
            logger.error("API call failed with resource access error after {} ms: {}", 
                        processingTime, e.getMessage());
            
            return ApiCallResult.failure(
                "Resource access error: " + e.getMessage(),
                HttpStatus.REQUEST_TIMEOUT,
                processingTime,
                e
            );

        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            logger.error("API call failed with unexpected error after {} ms: {}", 
                        processingTime, e.getMessage(), e);
            
            return ApiCallResult.failure(
                "Unexpected error: " + e.getMessage(),
                HttpStatus.INTERNAL_SERVER_ERROR,
                processingTime,
                e
            );
        }
    }

    /**
     * 带重试的API调用
     *
     * @param requestData 请求数据
     * @param targetApi 目标API配置
     * @return API响应结果
     */
    public ApiCallResult callApiWithRetry(Object requestData, TargetApiConfig targetApi) {
        if (!targetApi.isRetryEnabled()) {
            return callApi(requestData, targetApi);
        }

        int maxRetries = targetApi.getRetryCount();
        int retryInterval = targetApi.getRetryInterval();
        
        logger.info("Calling API with retry: max retries = {}, interval = {} ms", maxRetries, retryInterval);

        ApiCallResult lastResult = null;
        for (int attempt = 0; attempt <= maxRetries; attempt++) {
            if (attempt > 0) {
                logger.info("Retrying API call, attempt {} of {}", attempt, maxRetries);
                try {
                    Thread.sleep(retryInterval);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    logger.warn("Retry interrupted");
                    break;
                }
            }

            lastResult = callApi(requestData, targetApi);
            
            if (lastResult.isSuccess()) {
                if (attempt > 0) {
                    logger.info("API call succeeded on retry attempt {}", attempt);
                }
                return lastResult;
            }

            // 如果是客户端错误（4xx），不进行重试
            if (lastResult.getStatusCode() != null && 
                lastResult.getStatusCode().is4xxClientError()) {
                logger.info("Client error detected, skipping retries");
                break;
            }
        }

        logger.error("API call failed after {} attempts", maxRetries + 1);
        return lastResult;
    }

    /**
     * 创建HTTP请求头
     *
     * @param targetApi 目标API配置
     * @return HTTP请求头
     */
    private HttpHeaders createHttpHeaders(TargetApiConfig targetApi) {
        HttpHeaders headers = new HttpHeaders();
        
        // 设置默认Content-Type
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        // 添加自定义请求头
        if (targetApi.hasHeaders()) {
            for (Map.Entry<String, String> entry : targetApi.getHeaders().entrySet()) {
                headers.set(entry.getKey(), entry.getValue());
            }
        }
        
        return headers;
    }

    /**
     * 执行API调用
     *
     * @param url API地址
     * @param method HTTP方法
     * @param entity HTTP实体
     * @param targetApi 目标API配置
     * @return 响应结果
     */
    private ResponseEntity<Object> executeApiCall(String url, String method, HttpEntity<Object> entity, TargetApiConfig targetApi) {
        HttpMethod httpMethod = HttpMethod.valueOf(method.toUpperCase());
        
        switch (httpMethod) {
            case GET:
                return restTemplate.exchange(url, HttpMethod.GET, entity, Object.class);
            case POST:
                return restTemplate.exchange(url, HttpMethod.POST, entity, Object.class);
            case PUT:
                return restTemplate.exchange(url, HttpMethod.PUT, entity, Object.class);
            case DELETE:
                return restTemplate.exchange(url, HttpMethod.DELETE, entity, Object.class);
            case PATCH:
                return restTemplate.exchange(url, HttpMethod.PATCH, entity, Object.class);
            default:
                throw new TransformationException(
                    TransformationErrorType.CONFIG_ERROR,
                    "Unsupported HTTP method: " + method
                );
        }
    }

    /**
     * 解析内部API URL
     * 支持 internal:// 协议的内部服务调用
     *
     * @param url 原始URL
     * @return 解析后的URL
     */
    public String resolveInternalUrl(String url) {
        if (url == null) {
            return null;
        }

        // 处理内部协议 internal://
        if (url.startsWith("internal://")) {
            String internalPath = url.substring("internal://".length());
            // 这里可以根据实际情况配置内部服务的基础URL
            String baseUrl = getInternalServiceBaseUrl();
            return baseUrl + "/" + internalPath;
        }

        return url;
    }

    /**
     * 获取内部服务基础URL
     * 可以从配置文件或环境变量中获取
     *
     * @return 内部服务基础URL
     */
    private String getInternalServiceBaseUrl() {
        // 这里可以从配置中读取，暂时使用默认值
        return "http://localhost:8088";
    }

    /**
     * API调用结果
     */
    public static class ApiCallResult {
        private boolean success;
        private Object responseData;
        private String errorMessage;
        private HttpStatus statusCode;
        private long processingTime;
        private Exception exception;

        private ApiCallResult() {}

        public static ApiCallResult success(Object responseData, HttpStatus statusCode, long processingTime) {
            ApiCallResult result = new ApiCallResult();
            result.success = true;
            result.responseData = responseData;
            result.statusCode = statusCode;
            result.processingTime = processingTime;
            return result;
        }

        public static ApiCallResult failure(String errorMessage, HttpStatus statusCode, long processingTime, Exception exception) {
            ApiCallResult result = new ApiCallResult();
            result.success = false;
            result.errorMessage = errorMessage;
            result.statusCode = statusCode;
            result.processingTime = processingTime;
            result.exception = exception;
            return result;
        }

        // Getter methods
        public boolean isSuccess() {
            return success;
        }

        public Object getResponseData() {
            return responseData;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public HttpStatus getStatusCode() {
            return statusCode;
        }

        public long getProcessingTime() {
            return processingTime;
        }

        public Exception getException() {
            return exception;
        }
    }
}