package com.my.som.component.dataTransform;

import com.my.som.dto.dataTransform.ValidationConfig;
import com.my.som.exception.dataTransform.TransformationException;
import com.my.som.exception.dataTransform.TransformationErrorType;
import com.my.som.util.dataTransform.FieldPathUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.regex.Pattern;

/**
 * 请求参数验证器
 * 负责验证请求参数的格式、必填字段和数据类型
 */
@Component
public class RequestValidator {

    private static final Logger logger = LoggerFactory.getLogger(RequestValidator.class);

    @Autowired
    private FieldPathUtil fieldPathUtil;

    /**
     * 验证请求数据
     *
     * @param data 请求数据
     * @param config 验证配置
     */
    public void validate(Object data, ValidationConfig config) {
        if (config == null) {
            logger.debug("No validation config provided, skipping validation");
            return;
        }

        logger.debug("Starting request validation");
        List<String> errors = new ArrayList<>();

        try {
            // 验证必填字段
            if (config.getRequiredFields() != null && !config.getRequiredFields().isEmpty()) {
                validateRequiredFields(data, config.getRequiredFields(), errors);
            }

            // 验证字段格式
            if (config.getFieldFormats() != null && !config.getFieldFormats().isEmpty()) {
                validateFieldFormats(data, config.getFieldFormats(), errors);
            }

            // 验证字段类型
            if (config.getFieldTypes() != null && !config.getFieldTypes().isEmpty()) {
                validateFieldTypes(data, config.getFieldTypes(), errors);
            }

            // 验证字段值范围
            if (config.getFieldRanges() != null && !config.getFieldRanges().isEmpty()) {
                validateFieldRanges(data, config.getFieldRanges(), errors);
            }

            // 验证自定义规则
            if (config.getCustomRules() != null && !config.getCustomRules().isEmpty()) {
                validateCustomRules(data, config.getCustomRules(), errors);
            }

            // 如果有验证错误，抛出异常
            if (!errors.isEmpty()) {
                String errorMessage = "Validation failed: " + String.join("; ", errors);
                logger.error("Request validation failed: {}", errorMessage);
                throw new TransformationException(
                    TransformationErrorType.VALIDATION_ERROR,
                    errorMessage
                );
            }

            logger.debug("Request validation completed successfully");

        } catch (TransformationException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Unexpected error during validation: {}", e.getMessage(), e);
            throw new TransformationException(
                TransformationErrorType.VALIDATION_ERROR,
                "Validation error: " + e.getMessage(),
                e
            );
        }
    }

    /**
     * 验证必填字段
     *
     * @param data 数据
     * @param requiredFields 必填字段列表
     * @param errors 错误列表
     */
    private void validateRequiredFields(Object data, List<String> requiredFields, List<String> errors) {
        logger.debug("Validating {} required fields", requiredFields.size());

        for (String fieldPath : requiredFields) {
            try {
                Object value = fieldPathUtil.getValueByPath(data, fieldPath);
                if (value == null || (value instanceof String && !StringUtils.hasText((String) value))) {
                    errors.add("Required field '" + fieldPath + "' is missing or empty");
                    logger.debug("Required field '{}' validation failed", fieldPath);
                }
            } catch (Exception e) {
                errors.add("Error accessing required field '" + fieldPath + "': " + e.getMessage());
                logger.debug("Error accessing required field '{}': {}", fieldPath, e.getMessage());
            }
        }
    }

    /**
     * 验证字段格式
     *
     * @param data 数据
     * @param fieldFormats 字段格式配置
     * @param errors 错误列表
     */
    private void validateFieldFormats(Object data, Map<String, String> fieldFormats, List<String> errors) {
        logger.debug("Validating field formats for {} fields", fieldFormats.size());

        for (Map.Entry<String, String> entry : fieldFormats.entrySet()) {
            String fieldPath = entry.getKey();
            String formatPattern = entry.getValue();

            try {
                Object value = fieldPathUtil.getValueByPath(data, fieldPath);
                if (value != null) {
                    String stringValue = value.toString();
                    if (StringUtils.hasText(stringValue) && !Pattern.matches(formatPattern, stringValue)) {
                        errors.add("Field '" + fieldPath + "' format is invalid. Expected pattern: " + formatPattern);
                        logger.debug("Field '{}' format validation failed", fieldPath);
                    }
                }
            } catch (Exception e) {
                errors.add("Error validating format for field '" + fieldPath + "': " + e.getMessage());
                logger.debug("Error validating format for field '{}': {}", fieldPath, e.getMessage());
            }
        }
    }

    /**
     * 验证字段类型
     *
     * @param data 数据
     * @param fieldTypes 字段类型配置
     * @param errors 错误列表
     */
    private void validateFieldTypes(Object data, Map<String, String> fieldTypes, List<String> errors) {
        logger.debug("Validating field types for {} fields", fieldTypes.size());

        for (Map.Entry<String, String> entry : fieldTypes.entrySet()) {
            String fieldPath = entry.getKey();
            String expectedType = entry.getValue();

            try {
                Object value = fieldPathUtil.getValueByPath(data, fieldPath);
                if (value != null && !isValidType(value, expectedType)) {
                    errors.add("Field '" + fieldPath + "' type is invalid. Expected: " + expectedType + 
                              ", Actual: " + value.getClass().getSimpleName());
                    logger.debug("Field '{}' type validation failed", fieldPath);
                }
            } catch (Exception e) {
                errors.add("Error validating type for field '" + fieldPath + "': " + e.getMessage());
                logger.debug("Error validating type for field '{}': {}", fieldPath, e.getMessage());
            }
        }
    }

    /**
     * 验证字段值范围
     *
     * @param data 数据
     * @param fieldRanges 字段范围配置
     * @param errors 错误列表
     */
    private void validateFieldRanges(Object data, Map<String, ValidationConfig.RangeConfig> fieldRanges, List<String> errors) {
        logger.debug("Validating field ranges for {} fields", fieldRanges.size());

        for (Map.Entry<String, ValidationConfig.RangeConfig> entry : fieldRanges.entrySet()) {
            String fieldPath = entry.getKey();
            ValidationConfig.RangeConfig rangeConfig = entry.getValue();

            try {
                Object value = fieldPathUtil.getValueByPath(data, fieldPath);
                if (value != null && !isValidRange(value, rangeConfig)) {
                    errors.add("Field '" + fieldPath + "' value is out of range. " + 
                              "Min: " + rangeConfig.getMin() + ", Max: " + rangeConfig.getMax());
                    logger.debug("Field '{}' range validation failed", fieldPath);
                }
            } catch (Exception e) {
                errors.add("Error validating range for field '" + fieldPath + "': " + e.getMessage());
                logger.debug("Error validating range for field '{}': {}", fieldPath, e.getMessage());
            }
        }
    }

    /**
     * 验证自定义规则
     *
     * @param data 数据
     * @param customRules 自定义规则配置
     * @param errors 错误列表
     */
    private void validateCustomRules(Object data, List<ValidationConfig.CustomRule> customRules, List<String> errors) {
        logger.debug("Validating {} custom rules", customRules.size());

        for (ValidationConfig.CustomRule rule : customRules) {
            try {
                if (!evaluateCustomRule(data, rule)) {
                    errors.add("Custom rule '" + rule.getName() + "' validation failed: " + rule.getErrorMessage());
                    logger.debug("Custom rule '{}' validation failed", rule.getName());
                }
            } catch (Exception e) {
                errors.add("Error evaluating custom rule '" + rule.getName() + "': " + e.getMessage());
                logger.debug("Error evaluating custom rule '{}': {}", rule.getName(), e.getMessage());
            }
        }
    }

    /**
     * 检查值是否符合指定类型
     *
     * @param value 值
     * @param expectedType 期望类型
     * @return 是否符合类型
     */
    private boolean isValidType(Object value, String expectedType) {
        if (value == null) {
            return true; // null值跳过类型检查
        }

        switch (expectedType.toLowerCase()) {
            case "string":
                return value instanceof String;
            case "integer":
            case "int":
                return value instanceof Integer || value instanceof Long;
            case "number":
            case "double":
                return value instanceof Number;
            case "boolean":
                return value instanceof Boolean;
            case "array":
            case "list":
                return value instanceof List || value.getClass().isArray();
            case "object":
            case "map":
                return value instanceof Map;
            default:
                logger.warn("Unknown type '{}', skipping type validation", expectedType);
                return true;
        }
    }

    /**
     * 检查值是否在指定范围内
     *
     * @param value 值
     * @param rangeConfig 范围配置
     * @return 是否在范围内
     */
    private boolean isValidRange(Object value, ValidationConfig.RangeConfig rangeConfig) {
        if (value == null || rangeConfig == null) {
            return true;
        }

        try {
            if (value instanceof Number) {
                double numValue = ((Number) value).doubleValue();
                
                if (rangeConfig.getMin() != null) {
                    double minValue = rangeConfig.getMin().doubleValue();
                    if (numValue < minValue) {
                        return false;
                    }
                }
                
                if (rangeConfig.getMax() != null) {
                    double maxValue = rangeConfig.getMax().doubleValue();
                    if (numValue > maxValue) {
                        return false;
                    }
                }
                
                return true;
            } else if (value instanceof String) {
                String strValue = (String) value;
                int length = strValue.length();
                
                if (rangeConfig.getMin() != null) {
                    int minLength = rangeConfig.getMin().intValue();
                    if (length < minLength) {
                        return false;
                    }
                }
                
                if (rangeConfig.getMax() != null) {
                    int maxLength = rangeConfig.getMax().intValue();
                    if (length > maxLength) {
                        return false;
                    }
                }
                
                return true;
            }
        } catch (Exception e) {
            logger.warn("Error validating range for value '{}': {}", value, e.getMessage());
        }

        return true; // 如果无法验证，默认通过
    }

    /**
     * 评估自定义规则
     *
     * @param data 数据
     * @param rule 自定义规则
     * @return 是否通过验证
     */
    private boolean evaluateCustomRule(Object data, ValidationConfig.CustomRule rule) {
        // 这里可以实现更复杂的自定义规则评估逻辑
        // 目前简单实现，可以根据需要扩展
        
        String ruleType = rule.getType();
        if ("expression".equals(ruleType)) {
            // 可以集成表达式引擎来评估规则
            logger.debug("Evaluating expression rule: {}", rule.getExpression());
            // 简单实现：总是返回true，实际项目中可以集成SpEL或其他表达式引擎
            return true;
        }
        
        logger.warn("Unknown custom rule type '{}', skipping validation", ruleType);
        return true;
    }
}