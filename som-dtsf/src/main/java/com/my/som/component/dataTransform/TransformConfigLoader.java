package com.my.som.component.dataTransform;

import com.my.som.dto.dataTransform.RouteConfig;
import com.my.som.dto.dataTransform.RouteConfigContainer;
import com.my.som.dto.dataTransform.TransformConfig;
import com.my.som.dto.dataTransform.TransformConfigContainer;
import com.my.som.dto.dataTransform.ValidationResult;
import com.my.som.exception.dataTransform.ConfigurationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.yaml.snakeyaml.Yaml;
import org.yaml.snakeyaml.constructor.Constructor;
import org.yaml.snakeyaml.error.YAMLException;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 转换配置文件加载器
 * 支持业务独立配置文件架构、YAML配置文件解析、热加载机制和配置缓存
 */
@Component
public class TransformConfigLoader {

    private static final Logger logger = LoggerFactory.getLogger(TransformConfigLoader.class);

    /**
     * 路由配置文件路径
     */
    @Value("${route.config.path:classpath:config/route-config.yml}")
    private String routeConfigPath;

    /**
     * 转换配置文件路径（兼容旧版本）
     */
    @Value("${transform.config.path:classpath:config/transform-config.yml}")
    private String legacyTransformConfigPath;

    /**
     * 是否启用热加载
     */
    @Value("${transform.config.hotReload.enabled:true}")
    private boolean hotReloadEnabled;

    /**
     * 热加载检查间隔（秒）
     */
    @Value("${transform.config.hotReload.interval:30}")
    private int hotReloadInterval;

    /**
     * 转换配置缓存（按infno索引）
     */
    private final Map<String, TransformConfig> transformConfigCache = new ConcurrentHashMap<>();

    /**
     * 路由配置缓存（按infno索引）
     */
    private final Map<String, RouteConfig> routeConfigCache = new ConcurrentHashMap<>();

    /**
     * 转换配置文件路径缓存（按配置文件路径索引）
     */
    private final Map<String, TransformConfig> transformConfigFileCache = new ConcurrentHashMap<>();

    /**
     * 路由配置容器缓存
     */
    private volatile RouteConfigContainer routeConfigContainer;

    /**
     * 转换配置容器缓存（兼容旧版本）
     */
    private volatile TransformConfigContainer legacyTransformConfigContainer;

    /**
     * 配置文件最后修改时间映射
     */
    private final Map<String, Long> fileLastModifiedMap = new ConcurrentHashMap<>();

    /**
     * 读写锁，保证配置读取和更新的线程安全
     */
    private final ReadWriteLock lock = new ReentrantReadWriteLock();

    /**
     * 热加载调度器
     */
    private ScheduledExecutorService hotReloadScheduler;

    /**
     * YAML解析器（用于路由配置）
     */
    private final Yaml routeYaml;

    /**
     * YAML解析器（用于转换配置）
     */
    private final Yaml transformYaml;

    /**
     * 配置文件监听器
     */
    private WatchService watchService;
    private WatchKey watchKey;

    public TransformConfigLoader() {
        // 初始化YAML解析器
        Constructor routeConstructor = new Constructor(RouteConfigContainer.class);
        this.routeYaml = new Yaml(routeConstructor);
        
        Constructor transformConstructor = new Constructor(TransformConfigContainer.class);
        this.transformYaml = new Yaml(transformConstructor);
    }

    /**
     * 初始化配置加载器
     */
    @PostConstruct
    public void init() {
        try {
            // 加载路由配置和所有转换配置
            loadAllTransformConfigs();
            
            // 启动热加载机制
            if (hotReloadEnabled) {
                startHotReload();
            }
            
            logger.info("TransformConfigLoader initialized successfully. Route config: {}, Hot reload: {}", 
                    routeConfigPath, hotReloadEnabled);
        } catch (Exception e) {
            logger.error("Failed to initialize TransformConfigLoader", e);
            throw new ConfigurationException("CONFIG_INIT_ERROR", 
                    "Failed to initialize configuration loader", e);
        }
    }

    /**
     * 销毁配置加载器
     */
    @PreDestroy
    public void destroy() {
        try {
            // 停止热加载调度器
            if (hotReloadScheduler != null && !hotReloadScheduler.isShutdown()) {
                hotReloadScheduler.shutdown();
                if (!hotReloadScheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    hotReloadScheduler.shutdownNow();
                }
            }
            
            // 关闭文件监听器
            if (watchKey != null) {
                watchKey.cancel();
            }
            if (watchService != null) {
                watchService.close();
            }
            
            logger.info("TransformConfigLoader destroyed successfully");
        } catch (Exception e) {
            logger.error("Error during TransformConfigLoader destruction", e);
        }
    }

    /**
     * 从route-config中加载所有转换配置
     */
    public Map<String, TransformConfig> loadAllTransformConfigs() {
        lock.writeLock().lock();
        try {
            logger.info("Loading all transform configurations from route config: {}", routeConfigPath);
            
            // 1. 加载路由配置
            RouteConfigContainer routeContainer = loadRouteConfig();
            
            // 2. 清空缓存
            transformConfigCache.clear();
            routeConfigCache.clear();
            transformConfigFileCache.clear();
            
            // 3. 更新路由配置缓存
            if (routeContainer.hasRoutes()) {
                for (RouteConfig route : routeContainer.getRoutes()) {
                    if (route.getInfno() != null) {
                        routeConfigCache.put(route.getInfno(), route);
                    }
                }
            }
            
            // 4. 加载每个业务的转换配置
            Set<String> transformConfigPaths = routeContainer.getAllTransformConfigPaths();
            for (String configPath : transformConfigPaths) {
                try {
                    TransformConfig transformConfig = loadTransformConfig(configPath);
                    if (transformConfig != null && transformConfig.getInfno() != null) {
                        transformConfigCache.put(transformConfig.getInfno(), transformConfig);
                        transformConfigFileCache.put(configPath, transformConfig);
                    }
                } catch (Exception e) {
                    logger.error("Failed to load transform config from path: {}", configPath, e);
                    // 继续加载其他配置文件
                }
            }
            
            // 5. 尝试加载兼容旧版本的统一配置文件
            try {
                loadLegacyTransformConfigs();
            } catch (Exception e) {
                logger.debug("No legacy transform config found or failed to load: {}", e.getMessage());
            }
            
            // 6. 更新最后修改时间
            updateLastModified(routeConfigPath);
            for (String configPath : transformConfigPaths) {
                updateLastModified(configPath);
            }
            
            logger.info("Successfully loaded {} transform configurations from {} route configs", 
                    transformConfigCache.size(), routeConfigCache.size());
            
            return new HashMap<>(transformConfigCache);
            
        } catch (Exception e) {
            logger.error("Failed to load all transform configurations", e);
            throw ConfigurationException.configLoadError(routeConfigPath, e);
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 根据transformConfig路径加载单个转换配置
     */
    public TransformConfig loadTransformConfig(String configPath) {
        if (configPath == null || configPath.trim().isEmpty()) {
            return null;
        }
        
        try {
            logger.debug("Loading transform config from path: {}", configPath);
            
            // 读取配置文件内容
            String configContent = readConfigFile(configPath);
            
            // 验证配置格式
            ValidationResult validationResult = validateTransformConfig(configContent);
            if (!validationResult.isValid()) {
                throw ConfigurationException.configValidationError(configPath, 
                        "Transform configuration validation failed: " + validationResult.getErrors());
            }
            
            // 解析配置
            TransformConfig config = parseTransformConfig(configContent);
            
            logger.debug("Successfully loaded transform config: {}", config.getInfno());
            return config;
            
        } catch (ConfigurationException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Failed to load transform config from path: {}", configPath, e);
            throw ConfigurationException.configLoadError(configPath, e);
        }
    }

    /**
     * 加载路由配置
     */
    private RouteConfigContainer loadRouteConfig() {
        try {
            logger.debug("Loading route config from: {}", routeConfigPath);
            
            // 读取配置文件内容
            String configContent = readConfigFile(routeConfigPath);
            
            // 验证配置格式
            ValidationResult validationResult = validateRouteConfig(configContent);
            if (!validationResult.isValid()) {
                throw ConfigurationException.configValidationError(routeConfigPath, 
                        "Route configuration validation failed: " + validationResult.getErrors());
            }
            
            // 解析配置
            RouteConfigContainer container = parseRouteConfig(configContent);
            
            // 更新缓存
            this.routeConfigContainer = container;
            
            logger.debug("Successfully loaded route config with {} routes", container.getRouteCount());
            return container;
            
        } catch (Exception e) {
            logger.error("Failed to load route config", e);
            throw ConfigurationException.configLoadError(routeConfigPath, e);
        }
    }

    /**
     * 加载兼容旧版本的转换配置
     */
    private void loadLegacyTransformConfigs() {
        try {
            logger.debug("Loading legacy transform config from: {}", legacyTransformConfigPath);
            
            // 读取配置文件内容
            String configContent = readConfigFile(legacyTransformConfigPath);
            
            // 解析配置
            TransformConfigContainer container = parseTransformConfigContainer(configContent);
            
            // 更新缓存
            this.legacyTransformConfigContainer = container;
            
            // 将旧版本配置添加到缓存中（如果不存在的话）
            if (container.hasTransforms()) {
                for (TransformConfig config : container.getTransforms()) {
                    if (config.getInfno() != null && !transformConfigCache.containsKey(config.getInfno())) {
                        transformConfigCache.put(config.getInfno(), config);
                        logger.debug("Added legacy transform config: {}", config.getInfno());
                    }
                }
            }
            
            logger.debug("Successfully loaded legacy transform config");
            
        } catch (Exception e) {
            logger.debug("Failed to load legacy transform config: {}", e.getMessage());
            // 不抛出异常，因为这是兼容性功能
        }
    }

    /**
     * 加载转换配置（兼容旧版本方法）
     */
    public List<TransformConfig> loadConfigs() {
        Map<String, TransformConfig> configMap = loadAllTransformConfigs();
        return new ArrayList<>(configMap.values());
    }

    /**
     * 热加载所有转换配置
     */
    public void reloadAllConfigs() {
        try {
            logger.info("Reloading all transform configurations...");
            
            // 检查路由配置文件是否有变化
            if (!hasConfigChanged(routeConfigPath)) {
                logger.debug("Route configuration file has not changed, checking individual transform configs");
                
                // 检查各个转换配置文件是否有变化
                boolean anyTransformConfigChanged = false;
                if (routeConfigContainer != null) {
                    for (String configPath : routeConfigContainer.getAllTransformConfigPaths()) {
                        if (hasConfigChanged(configPath)) {
                            anyTransformConfigChanged = true;
                            break;
                        }
                    }
                }
                
                if (!anyTransformConfigChanged) {
                    logger.debug("No configuration files have changed, skipping reload");
                    return;
                }
            }
            
            // 重新加载所有配置
            loadAllTransformConfigs();
            
            logger.info("All transform configurations reloaded successfully");
            
        } catch (Exception e) {
            logger.error("Failed to reload all transform configurations", e);
            throw ConfigurationException.configReloadError(routeConfigPath, e);
        }
    }

    /**
     * 热加载指定路径的转换配置
     */
    public void reloadConfig(String configPath) {
        if (configPath == null || configPath.trim().isEmpty()) {
            return;
        }
        
        lock.writeLock().lock();
        try {
            logger.info("Reloading transform config from path: {}", configPath);
            
            // 检查文件是否有变化
            if (!hasConfigChanged(configPath)) {
                logger.debug("Transform config file has not changed, skipping reload: {}", configPath);
                return;
            }
            
            // 重新加载指定配置
            TransformConfig config = loadTransformConfig(configPath);
            if (config != null && config.getInfno() != null) {
                transformConfigCache.put(config.getInfno(), config);
                transformConfigFileCache.put(configPath, config);
                updateLastModified(configPath);
            }
            
            logger.info("Transform config reloaded successfully: {}", configPath);
            
        } catch (Exception e) {
            logger.error("Failed to reload transform config: {}", configPath, e);
            throw ConfigurationException.configReloadError(configPath, e);
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 热加载配置（兼容旧版本方法）
     */
    public void reloadConfigs() {
        reloadAllConfigs();
    }

    /**
     * 根据infno获取配置（从route-config中查找对应的transformConfig路径）
     */
    public TransformConfig getConfigByInfono(String infno) {
        if (infno == null || infno.trim().isEmpty()) {
            return null;
        }
        
        lock.readLock().lock();
        try {
            return transformConfigCache.get(infno);
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * 获取route配置中指定infno的transformConfig路径
     */
    public String getTransformConfigPath(String infno) {
        if (infno == null || infno.trim().isEmpty()) {
            return null;
        }
        
        lock.readLock().lock();
        try {
            RouteConfig routeConfig = routeConfigCache.get(infno);
            return routeConfig != null ? routeConfig.getTransformConfig() : null;
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * 获取所有配置
     */
    public List<TransformConfig> getAllConfigs() {
        lock.readLock().lock();
        try {
            return new ArrayList<>(transformConfigCache.values());
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * 获取所有已加载的转换配置路径
     */
    public Set<String> getLoadedConfigPaths() {
        lock.readLock().lock();
        try {
            return new HashSet<>(transformConfigFileCache.keySet());
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * 获取配置容器（兼容旧版本方法）
     */
    public TransformConfigContainer getConfigContainer() {
        lock.readLock().lock();
        try {
            return legacyTransformConfigContainer;
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * 获取路由配置容器
     */
    public RouteConfigContainer getRouteConfigContainer() {
        lock.readLock().lock();
        try {
            return routeConfigContainer;
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * 验证转换配置文件格式
     */
    public ValidationResult validateTransformConfig(String configContent) {
        ValidationResult result = new ValidationResult();
        
        try {
            if (configContent == null || configContent.trim().isEmpty()) {
                result.addError("Transform configuration content is empty");
                return result;
            }
            
            // 尝试解析YAML
            TransformConfig config = parseTransformConfig(configContent);
            
            if (config == null) {
                result.addError("Failed to parse transform configuration YAML");
                return result;
            }
            
            // 验证配置结构
            validateTransformConfigStructure(config, result);
            
        } catch (YAMLException e) {
            result.addError("Transform config YAML format error: " + e.getMessage());
        } catch (Exception e) {
            result.addError("Transform configuration validation error: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 验证route配置文件格式
     */
    public ValidationResult validateRouteConfig(String configContent) {
        ValidationResult result = new ValidationResult();
        
        try {
            if (configContent == null || configContent.trim().isEmpty()) {
                result.addError("Route configuration content is empty");
                return result;
            }
            
            // 尝试解析YAML
            RouteConfigContainer container = parseRouteConfig(configContent);
            
            if (container == null) {
                result.addError("Failed to parse route configuration YAML");
                return result;
            }
            
            // 验证配置结构
            validateRouteConfigStructure(container, result);
            
        } catch (YAMLException e) {
            result.addError("Route config YAML format error: " + e.getMessage());
        } catch (Exception e) {
            result.addError("Route configuration validation error: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 验证配置格式（兼容旧版本方法）
     */
    public ValidationResult validateConfig(String configContent) {
        return validateTransformConfig(configContent);
    }

    /**
     * 检查配置是否有效
     */
    public boolean isConfigValid() {
        lock.readLock().lock();
        try {
            return !transformConfigCache.isEmpty() || 
                   (legacyTransformConfigContainer != null && legacyTransformConfigContainer.hasTransforms());
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * 获取配置统计信息
     */
    public Map<String, Object> getConfigStats() {
        lock.readLock().lock();
        try {
            Map<String, Object> stats = new HashMap<>();
            stats.put("routeConfigPath", routeConfigPath);
            stats.put("legacyTransformConfigPath", legacyTransformConfigPath);
            stats.put("hotReloadEnabled", hotReloadEnabled);
            stats.put("totalTransformConfigs", transformConfigCache.size());
            stats.put("totalRouteConfigs", routeConfigCache.size());
            stats.put("loadedConfigPaths", transformConfigFileCache.keySet());
            stats.put("enabledTransformConfigs", transformConfigCache.values().stream()
                    .mapToInt(config -> config.isEnabled() ? 1 : 0).sum());
            
            // 添加文件修改时间信息
            Map<String, Date> fileModificationTimes = new HashMap<>();
            for (Map.Entry<String, Long> entry : fileLastModifiedMap.entrySet()) {
                fileModificationTimes.put(entry.getKey(), new Date(entry.getValue()));
            }
            stats.put("fileLastModified", fileModificationTimes);
            
            return stats;
        } finally {
            lock.readLock().unlock();
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 读取配置文件内容
     */
    private String readConfigFile(String configPath) throws IOException {
        if (configPath.startsWith("classpath:")) {
            // 从classpath读取
            String resourcePath = configPath.substring("classpath:".length());
            try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(resourcePath)) {
                if (inputStream == null) {
                    throw new FileNotFoundException("Configuration file not found in classpath: " + resourcePath);
                }
                return readInputStream(inputStream);
            }
        } else {
            // 从文件系统读取
            Path path = Paths.get(configPath);
            if (!Files.exists(path)) {
                throw new FileNotFoundException("Configuration file not found: " + configPath);
            }
            return new String(Files.readAllBytes(path), "UTF-8");
        }
    }

    /**
     * 读取输入流内容
     */
    private String readInputStream(InputStream inputStream) throws IOException {
        StringBuilder content = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"))) {
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
        }
        return content.toString();
    }

    /**
     * 解析路由配置内容
     */
    private RouteConfigContainer parseRouteConfig(String configContent) {
        try {
            // 使用通用的YAML解析器来解析RouteConfigContainer
            Yaml routeConfigYaml = new Yaml();
            return routeConfigYaml.loadAs(configContent, RouteConfigContainer.class);
        } catch (YAMLException e) {
            throw ConfigurationException.configFormatError(routeConfigPath, e);
        }
    }

    /**
     * 解析单个转换配置内容
     */
    private TransformConfig parseTransformConfig(String configContent) {
        try {
            // 使用通用的YAML解析器来解析单个TransformConfig
            Yaml singleConfigYaml = new Yaml();
            return singleConfigYaml.loadAs(configContent, TransformConfig.class);
        } catch (YAMLException e) {
            throw ConfigurationException.configFormatError("transform-config", e);
        }
    }

    /**
     * 解析转换配置容器内容（兼容旧版本）
     */
    private TransformConfigContainer parseTransformConfigContainer(String configContent) {
        try {
            return transformYaml.load(configContent);
        } catch (YAMLException e) {
            throw ConfigurationException.configFormatError(legacyTransformConfigPath, e);
        }
    }

    /**
     * 验证路由配置结构
     */
    private void validateRouteConfigStructure(RouteConfigContainer container, ValidationResult result) {
        if (container == null) {
            result.addError("Route configuration container is null");
            return;
        }
        
        // 验证routes配置
        if (!container.hasRoutes()) {
            result.addError("No route configurations found");
        } else {
            List<RouteConfig> routes = container.getRoutes();
            Set<String> infnoSet = new HashSet<>();
            
            for (int i = 0; i < routes.size(); i++) {
                RouteConfig config = routes.get(i);
                String prefix = "routes[" + i + "]";
                
                // 验证基本字段
                if (config.getInfno() == null || config.getInfno().trim().isEmpty()) {
                    result.addError(prefix + ".infno is required");
                } else {
                    // 检查infno重复
                    if (infnoSet.contains(config.getInfno())) {
                        result.addError(prefix + ".infno '" + config.getInfno() + "' is duplicated");
                    } else {
                        infnoSet.add(config.getInfno());
                    }
                }
                
                if (config.getServiceType() == null || config.getServiceType().trim().isEmpty()) {
                    result.addError(prefix + ".serviceType is required");
                }
                
                // 验证转换配置路径
                if (config.getTransformConfig() != null && !config.getTransformConfig().trim().isEmpty()) {
                    String transformConfigPath = config.getTransformConfig();
                    if (!isValidConfigPath(transformConfigPath)) {
                        result.addWarning(prefix + ".transformConfig path may not exist: " + transformConfigPath);
                    }
                }
            }
        }
        
        // 验证全局配置
        if (container.hasGlobal()) {
            validateGlobalConfig(container.getGlobal(), result);
        }
    }

    /**
     * 验证转换配置结构
     */
    private void validateTransformConfigStructure(TransformConfig config, ValidationResult result) {
        if (config == null) {
            result.addError("Transform configuration is null");
            return;
        }
        
        // 验证基本字段
        if (config.getInfno() == null || config.getInfno().trim().isEmpty()) {
            result.addError("infno is required");
        }
        
        if (config.getServiceType() == null || config.getServiceType().trim().isEmpty()) {
            result.addError("serviceType is required");
        }
        
        // 验证转换配置
        if (config.getResponseTransform() == null && config.getRequestTransform() == null) {
            result.addWarning("No transform configurations found");
        }
        
        // 验证响应转换配置
        if (config.getResponseTransform() != null && config.getResponseTransform().isEnabled()) {
            validateResponseTransformConfig(config.getResponseTransform(), "responseTransform", result);
        }
    }

    /**
     * 验证配置结构（兼容旧版本方法）
     */
    private void validateConfigStructure(TransformConfigContainer container, ValidationResult result) {
        if (container == null) {
            result.addError("Configuration container is null");
            return;
        }
        
        // 验证transforms配置
        if (!container.hasTransforms()) {
            result.addWarning("No transform configurations found");
        } else {
            List<TransformConfig> transforms = container.getTransforms();
            Set<String> infnoSet = new HashSet<>();
            
            for (int i = 0; i < transforms.size(); i++) {
                TransformConfig config = transforms.get(i);
                String prefix = "transforms[" + i + "]";
                
                // 验证基本字段
                if (config.getInfno() == null || config.getInfno().trim().isEmpty()) {
                    result.addError(prefix + ".infno is required");
                } else {
                    // 检查infno重复
                    if (infnoSet.contains(config.getInfno())) {
                        result.addError(prefix + ".infno '" + config.getInfno() + "' is duplicated");
                    } else {
                        infnoSet.add(config.getInfno());
                    }
                }
                
                if (config.getServiceType() == null || config.getServiceType().trim().isEmpty()) {
                    result.addError(prefix + ".serviceType is required");
                }
                
                // 验证转换配置
                if (config.getResponseTransform() == null && config.getRequestTransform() == null) {
                    result.addWarning(prefix + " has no transform configurations");
                }
                
                // 验证响应转换配置
                if (config.getResponseTransform() != null && config.getResponseTransform().isEnabled()) {
                    validateResponseTransformConfig(config.getResponseTransform(), prefix + ".responseTransform", result);
                }
            }
        }
        
        // 验证全局配置
        if (container.hasGlobal()) {
            validateGlobalConfig(container.getGlobal(), result);
        }
    }

    /**
     * 验证响应转换配置
     */
    private void validateResponseTransformConfig(com.my.som.dto.dataTransform.ResponseTransformConfig responseTransform, 
                                               String prefix, ValidationResult result) {
        if (responseTransform.getTargets() == null || responseTransform.getTargets().isEmpty()) {
            result.addWarning(prefix + " has no target configurations");
        }
    }

    /**
     * 验证全局配置
     */
    private void validateGlobalConfig(Object global, ValidationResult result) {
        // 简化全局配置验证，因为结构可能不同
        if (global == null) {
            result.addWarning("Global configuration is null");
        }
    }

    /**
     * 检查配置文件路径是否有效
     */
    private boolean isValidConfigPath(String configPath) {
        if (configPath == null || configPath.trim().isEmpty()) {
            return false;
        }
        
        try {
            if (configPath.startsWith("classpath:")) {
                String resourcePath = configPath.substring("classpath:".length());
                return getClass().getClassLoader().getResource(resourcePath) != null;
            } else {
                return Files.exists(Paths.get(configPath));
            }
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 更新缓存（兼容旧版本方法）
     */
    private void updateCache(TransformConfigContainer container) {
        this.legacyTransformConfigContainer = container;
        
        // 更新配置缓存
        if (container.hasTransforms()) {
            for (TransformConfig config : container.getTransforms()) {
                if (config.getInfno() != null) {
                    transformConfigCache.put(config.getInfno(), config);
                }
            }
        }
        
        logger.debug("Legacy configuration cache updated with {} entries", transformConfigCache.size());
    }

    /**
     * 更新指定文件的最后修改时间
     */
    private void updateLastModified(String configPath) {
        try {
            if (!configPath.startsWith("classpath:")) {
                Path path = Paths.get(configPath);
                if (Files.exists(path)) {
                    fileLastModifiedMap.put(configPath, Files.getLastModifiedTime(path).toMillis());
                }
            } else {
                fileLastModifiedMap.put(configPath, System.currentTimeMillis());
            }
        } catch (Exception e) {
            logger.warn("Failed to update last modified time for: {}", configPath, e);
            fileLastModifiedMap.put(configPath, System.currentTimeMillis());
        }
    }

    /**
     * 检查指定配置文件是否有变化
     */
    private boolean hasConfigChanged(String configPath) {
        try {
            if (configPath.startsWith("classpath:")) {
                // classpath资源无法检测变化，总是返回false
                return false;
            }
            
            Path path = Paths.get(configPath);
            if (!Files.exists(path)) {
                return false;
            }
            
            long currentModified = Files.getLastModifiedTime(path).toMillis();
            Long lastModified = fileLastModifiedMap.get(configPath);
            
            return lastModified == null || currentModified > lastModified;
            
        } catch (Exception e) {
            logger.warn("Failed to check config file changes for: {}", configPath, e);
            return false;
        }
    }

    /**
     * 启动热加载机制
     */
    private void startHotReload() {
        if (routeConfigPath.startsWith("classpath:")) {
            logger.info("Hot reload is not supported for classpath resources");
            return;
        }
        
        // 启动定时检查
        hotReloadScheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread thread = new Thread(r, "transform-config-hot-reload");
            thread.setDaemon(true);
            return thread;
        });
        
        hotReloadScheduler.scheduleWithFixedDelay(() -> {
            try {
                // 检查路由配置文件变化
                boolean routeConfigChanged = hasConfigChanged(routeConfigPath);
                
                // 检查各个转换配置文件变化
                boolean anyTransformConfigChanged = false;
                if (routeConfigContainer != null) {
                    for (String configPath : routeConfigContainer.getAllTransformConfigPaths()) {
                        if (hasConfigChanged(configPath)) {
                            anyTransformConfigChanged = true;
                            break;
                        }
                    }
                }
                
                if (routeConfigChanged || anyTransformConfigChanged) {
                    logger.info("Configuration files changed, reloading...");
                    reloadAllConfigs();
                }
            } catch (Exception e) {
                logger.error("Error during hot reload check", e);
            }
        }, hotReloadInterval, hotReloadInterval, TimeUnit.SECONDS);
        
        logger.info("Hot reload scheduler started with interval: {} seconds", hotReloadInterval);
    }
}