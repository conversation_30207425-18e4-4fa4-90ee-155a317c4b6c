package com.my.som.dto.dataTransform;

import java.util.List;
import java.util.Map;

/**
 * 验证配置模型
 */
public class ValidationConfig {

    /**
     * 是否启用验证
     */
    private boolean enabled = true;

    /**
     * 必填字段列表
     */
    private List<String> requiredFields;

    /**
     * 字段类型验证规则
     */
    private Map<String, String> fieldTypes;

    /**
     * 字段长度限制
     */
    private Map<String, Integer> fieldLengths;

    /**
     * 正则表达式验证规则
     */
    private Map<String, String> regexPatterns;

    /**
     * 数值范围验证
     */
    private Map<String, NumericRange> numericRanges;

    /**
     * 枚举值验证
     */
    private Map<String, List<Object>> enumValues;

    /**
     * 自定义验证器
     */
    private List<String> customValidators;

    /**
     * 字段格式验证（正则表达式）
     */
    private Map<String, String> fieldFormats;

    /**
     * 字段范围验证
     */
    private Map<String, RangeConfig> fieldRanges;

    /**
     * 自定义规则列表
     */
    private List<CustomRule> customRules;

    /**
     * 验证失败时的行为（fail: 失败, warn: 警告, ignore: 忽略）
     */
    private String onValidationFailure = "fail";

    /**
     * 验证描述
     */
    private String description;

    // 构造函数
    public ValidationConfig() {
    }

    // Getter and Setter methods
    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public List<String> getRequiredFields() {
        return requiredFields;
    }

    public void setRequiredFields(List<String> requiredFields) {
        this.requiredFields = requiredFields;
    }

    public Map<String, String> getFieldTypes() {
        return fieldTypes;
    }

    public void setFieldTypes(Map<String, String> fieldTypes) {
        this.fieldTypes = fieldTypes;
    }

    public Map<String, Integer> getFieldLengths() {
        return fieldLengths;
    }

    public void setFieldLengths(Map<String, Integer> fieldLengths) {
        this.fieldLengths = fieldLengths;
    }

    public Map<String, String> getRegexPatterns() {
        return regexPatterns;
    }

    public void setRegexPatterns(Map<String, String> regexPatterns) {
        this.regexPatterns = regexPatterns;
    }

    public Map<String, NumericRange> getNumericRanges() {
        return numericRanges;
    }

    public void setNumericRanges(Map<String, NumericRange> numericRanges) {
        this.numericRanges = numericRanges;
    }

    public Map<String, List<Object>> getEnumValues() {
        return enumValues;
    }

    public void setEnumValues(Map<String, List<Object>> enumValues) {
        this.enumValues = enumValues;
    }

    public List<String> getCustomValidators() {
        return customValidators;
    }

    public void setCustomValidators(List<String> customValidators) {
        this.customValidators = customValidators;
    }

    public String getOnValidationFailure() {
        return onValidationFailure;
    }

    public void setOnValidationFailure(String onValidationFailure) {
        this.onValidationFailure = onValidationFailure;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Map<String, String> getFieldFormats() {
        return fieldFormats;
    }

    public void setFieldFormats(Map<String, String> fieldFormats) {
        this.fieldFormats = fieldFormats;
    }

    public Map<String, RangeConfig> getFieldRanges() {
        return fieldRanges;
    }

    public void setFieldRanges(Map<String, RangeConfig> fieldRanges) {
        this.fieldRanges = fieldRanges;
    }

    public List<CustomRule> getCustomRules() {
        return customRules;
    }

    public void setCustomRules(List<CustomRule> customRules) {
        this.customRules = customRules;
    }

    /**
     * 是否有必填字段验证
     */
    public boolean hasRequiredFields() {
        return requiredFields != null && !requiredFields.isEmpty();
    }

    /**
     * 是否有类型验证
     */
    public boolean hasFieldTypes() {
        return fieldTypes != null && !fieldTypes.isEmpty();
    }

    /**
     * 是否有长度验证
     */
    public boolean hasFieldLengths() {
        return fieldLengths != null && !fieldLengths.isEmpty();
    }

    /**
     * 是否有正则验证
     */
    public boolean hasRegexPatterns() {
        return regexPatterns != null && !regexPatterns.isEmpty();
    }

    /**
     * 是否有数值范围验证
     */
    public boolean hasNumericRanges() {
        return numericRanges != null && !numericRanges.isEmpty();
    }

    /**
     * 是否有枚举值验证
     */
    public boolean hasEnumValues() {
        return enumValues != null && !enumValues.isEmpty();
    }

    /**
     * 是否有自定义验证器
     */
    public boolean hasCustomValidators() {
        return customValidators != null && !customValidators.isEmpty();
    }

    /**
     * 是否有字段格式验证
     */
    public boolean hasFieldFormats() {
        return fieldFormats != null && !fieldFormats.isEmpty();
    }

    /**
     * 是否有字段范围验证
     */
    public boolean hasFieldRanges() {
        return fieldRanges != null && !fieldRanges.isEmpty();
    }

    /**
     * 是否有自定义规则
     */
    public boolean hasCustomRules() {
        return customRules != null && !customRules.isEmpty();
    }

    /**
     * 验证失败行为常量
     */
    public static class OnValidationFailure {
        public static final String FAIL = "fail";
        public static final String WARN = "warn";
        public static final String IGNORE = "ignore";
    }

    /**
     * 数值范围配置
     */
    public static class NumericRange {
        private Double min;
        private Double max;
        private boolean minInclusive = true;
        private boolean maxInclusive = true;

        public NumericRange() {
        }

        public NumericRange(Double min, Double max) {
            this.min = min;
            this.max = max;
        }

        // Getter and Setter methods
        public Double getMin() {
            return min;
        }

        public void setMin(Double min) {
            this.min = min;
        }

        public Double getMax() {
            return max;
        }

        public void setMax(Double max) {
            this.max = max;
        }

        public boolean isMinInclusive() {
            return minInclusive;
        }

        public void setMinInclusive(boolean minInclusive) {
            this.minInclusive = minInclusive;
        }

        public boolean isMaxInclusive() {
            return maxInclusive;
        }

        public void setMaxInclusive(boolean maxInclusive) {
            this.maxInclusive = maxInclusive;
        }
    }

    /**
     * 范围配置（支持数值和字符串长度）
     */
    public static class RangeConfig {
        private Number min;
        private Number max;
        private boolean minInclusive = true;
        private boolean maxInclusive = true;

        public RangeConfig() {
        }

        public RangeConfig(Number min, Number max) {
            this.min = min;
            this.max = max;
        }

        // Getter and Setter methods
        public Number getMin() {
            return min;
        }

        public void setMin(Number min) {
            this.min = min;
        }

        public Number getMax() {
            return max;
        }

        public void setMax(Number max) {
            this.max = max;
        }

        public boolean isMinInclusive() {
            return minInclusive;
        }

        public void setMinInclusive(boolean minInclusive) {
            this.minInclusive = minInclusive;
        }

        public boolean isMaxInclusive() {
            return maxInclusive;
        }

        public void setMaxInclusive(boolean maxInclusive) {
            this.maxInclusive = maxInclusive;
        }
    }

    /**
     * 自定义规则配置
     */
    public static class CustomRule {
        private String name;
        private String type;
        private String expression;
        private String errorMessage;
        private Map<String, Object> parameters;

        public CustomRule() {
        }

        public CustomRule(String name, String type, String expression, String errorMessage) {
            this.name = name;
            this.type = type;
            this.expression = expression;
            this.errorMessage = errorMessage;
        }

        // Getter and Setter methods
        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getExpression() {
            return expression;
        }

        public void setExpression(String expression) {
            this.expression = expression;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public Map<String, Object> getParameters() {
            return parameters;
        }

        public void setParameters(Map<String, Object> parameters) {
            this.parameters = parameters;
        }
    }
}