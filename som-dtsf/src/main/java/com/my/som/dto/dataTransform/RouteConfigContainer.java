package com.my.som.dto.dataTransform;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 路由配置容器
 * 用于解析和管理route-config.yml文件中的所有路由配置
 */
public class RouteConfigContainer {

    /**
     * 路由配置列表
     */
    private List<RouteConfig> routes;

    /**
     * 全局配置
     */
    private Map<String, Object> global;

    public List<RouteConfig> getRoutes() {
        return routes;
    }

    public void setRoutes(List<RouteConfig> routes) {
        this.routes = routes;
    }

    public Map<String, Object> getGlobal() {
        return global;
    }

    public void setGlobal(Map<String, Object> global) {
        this.global = global;
    }

    /**
     * 根据infno查找路由配置
     * 
     * @param infno 交易编号
     * @return 匹配的路由配置，如果未找到返回null
     */
    public RouteConfig findRouteByInfono(String infno) {
        if (routes == null || infno == null) {
            return null;
        }

        return routes.stream()
                .filter(route -> infno.equals(route.getInfno()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取所有转换配置文件路径
     * 
     * @return 转换配置文件路径集合
     */
    public Set<String> getAllTransformConfigPaths() {
        if (routes == null) {
            return new HashSet<>();
        }

        return routes.stream()
                .map(RouteConfig::getTransformConfig)
                .filter(path -> path != null && !path.trim().isEmpty())
                .collect(Collectors.toSet());
    }

    /**
     * 检查是否有路由配置
     * 
     * @return 是否有路由配置
     */
    public boolean hasRoutes() {
        return routes != null && !routes.isEmpty();
    }

    /**
     * 检查是否有全局配置
     * 
     * @return 是否有全局配置
     */
    public boolean hasGlobal() {
        return global != null && !global.isEmpty();
    }

    /**
     * 获取路由配置数量
     * 
     * @return 路由配置数量
     */
    public int getRouteCount() {
        return routes != null ? routes.size() : 0;
    }

    /**
     * 获取有转换配置的路由数量
     * 
     * @return 有转换配置的路由数量
     */
    public int getTransformEnabledRouteCount() {
        if (routes == null) {
            return 0;
        }

        return (int) routes.stream()
                .filter(route -> route.getTransformConfig() != null && !route.getTransformConfig().trim().isEmpty())
                .count();
    }

    /**
     * 验证配置容器的有效性
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        if (!hasRoutes()) {
            return false;
        }

        // 验证每个路由配置
        for (RouteConfig route : routes) {
            if (!route.isValidConfiguration()) {
                return false;
            }
        }

        return true;
    }
}