package com.my.som.dto.dataTransform;

import java.util.Map;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;

/**
 * 路由配置DTO
 */
public class RouteConfig {
    
    @NotBlank(message = "路由匹配值不能为空") 
    private String infno;


    @NotBlank(message = "服务类型不能为空")
    @Pattern(regexp = "^(thirdPartyCall|parameterTransform|dataExpose|dataBase)$", message = "服务类型必须为thirdPartyCall、parameterTransform、dataExpose或dataBase")
    private String serviceType;
    
    /**
     * 服务器类型，用于区分HTTP调用和数据库操作
     * @deprecated 使用serviceType字段代替，将在未来版本中移除
     */
    @Deprecated
    @Pattern(regexp = "^(http|dataBase)?$", message = "服务器类型必须为http或dataBase")
    private String serverType;
    
    @NotBlank(message = "目标URL不能为空")
    private String targetUrl;
    
    @NotBlank(message = "HTTP方法不能为空")
    @Pattern(regexp = "^(GET|POST|PUT|DELETE)$", message = "HTTP方法必须为GET、POST、PUT或DELETE")
    private String method;
    
    private Map<String, String> headers;
    
    @Min(value = 1000, message = "超时时间不能小于1000毫秒")
    private int timeout;
    
    @Pattern(regexp = "^(JSON|XML)?$", message = "目标格式必须为JSON或XML")
    private String targetFormat;
    
    @Pattern(regexp = "^(JSON|XML)?$", message = "请求格式必须为JSON或XML")
    private String requestFormat;
    
    @Pattern(regexp = "^(JSON|XML|json|resultset)?$", message = "响应格式必须为JSON、XML、json或resultset")
    private String responseFormat;
    
    @Pattern(regexp = "^(wrapped|direct)?$", message = "响应类型必须为wrapped或direct")
    private String responseType;
    
    /**
     * 数据源配置，当serverType为dataBase时使用
     */
    private DataSourceConfig dataSource;
    
    /**
     * SQL查询语句，当serverType为dataBase时使用
     */
    private String sourceQuery;
    
    /**
     * 转换配置文件路径，指向业务独立的转换配置文件
     */
    private String transformConfig;
    
    public String getInfoValue() {
        return infno;
    }
    
    public void setInfoValue(String infno) {
        this.infno = infno;
    }
    
    public String getInfno() {
        return infno;
    }
    
    public void setInfno(String infno) {
        this.infno = infno;
    }
    
    public String getServiceType() {
        return serviceType;
    }
    
    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }
    
    public String getTargetUrl() {
        return targetUrl;
    }
    
    public void setTargetUrl(String targetUrl) {
        this.targetUrl = targetUrl;
    }
    
    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }
    
    public Map<String, String> getHeaders() {
        return headers;
    }
    
    public void setHeaders(Map<String, String> headers) {
        this.headers = headers;
    }
    
    public int getTimeout() {
        return timeout;
    }
    
    public void setTimeout(int timeout) {
        this.timeout = timeout;
    }
    
    public String getTargetFormat() {
        return targetFormat;
    }
    
    public void setTargetFormat(String targetFormat) {
        this.targetFormat = targetFormat;
    }
    
    public String getRequestFormat() {
        return requestFormat;
    }
    
    public void setRequestFormat(String requestFormat) {
        this.requestFormat = requestFormat;
    }
    
    public String getResponseFormat() {
        return responseFormat;
    }
    
    public void setResponseFormat(String responseFormat) {
        this.responseFormat = responseFormat;
    }
    
    public String getResponseType() {
        return responseType;
    }
    
    public void setResponseType(String responseType) {
        this.responseType = responseType;
    }
    
    public String getServerType() {
        return serverType;
    }
    
    public void setServerType(String serverType) {
        this.serverType = serverType;
    }
    
    public DataSourceConfig getDataSource() {
        return dataSource;
    }
    
    public void setDataSource(DataSourceConfig dataSource) {
        this.dataSource = dataSource;
    }
    
    public String getSourceQuery() {
        return sourceQuery;
    }
    
    public void setSourceQuery(String sourceQuery) {
        this.sourceQuery = sourceQuery;
    }
    
    public String getTransformConfig() {
        return transformConfig;
    }
    
    public void setTransformConfig(String transformConfig) {
        this.transformConfig = transformConfig;
    }
    
    /**
     * 获取有效的请求格式，如果未配置则返回默认的JSON
     */
    public String getEffectiveRequestFormat() {
        return (requestFormat != null && !requestFormat.trim().isEmpty()) ? requestFormat : "JSON";
    }
    
    /**
     * 获取有效的响应格式，如果未配置则返回默认的json
     */
    public String getEffectiveResponseFormat() {
        if (responseFormat != null && !responseFormat.trim().isEmpty()) {
            // 标准化格式：将JSON转换为json，保持resultset不变
            if ("JSON".equalsIgnoreCase(responseFormat)) {
                return "json";
            } else if ("XML".equalsIgnoreCase(responseFormat)) {
                return "json"; // XML格式也映射到json，保持向后兼容
            } else if ("resultset".equalsIgnoreCase(responseFormat)) {
                return "resultset";
            } else if ("json".equalsIgnoreCase(responseFormat)) {
                return "json";
            }
        }
        return "json"; // 默认使用json格式
    }
    
    /**
     * 获取有效的响应类型，如果未配置则返回默认的wrapped
     */
    public String getEffectiveResponseType() {
        return (responseType != null && !responseType.trim().isEmpty()) ? responseType : "wrapped";
    }
    
    /**
     * 检查是否启用了双向转换
     */
    public boolean isDualTransformationEnabled() {
        return (requestFormat != null && !requestFormat.trim().isEmpty()) || 
               (responseFormat != null && !responseFormat.trim().isEmpty());
    }
    
    /**
     * 获取有效的服务器类型，根据serviceType推断服务器类型
     * 如果serviceType为"dataBase"，返回"dataBase"
     * 否则检查serverType字段（向后兼容），如果未配置则返回默认的"http"
     */
    public String getEffectiveServerType() {
        // 新逻辑：如果serviceType为dataBase，直接返回dataBase
        if ("dataBase".equals(serviceType)) {
            return "dataBase";
        }
        
        // 向后兼容：检查serverType字段
        return (serverType != null && !serverType.trim().isEmpty()) ? serverType : "http";
    }
    
    /**
     * 检查是否为数据库操作
     * 支持新的serviceType="dataBase"格式和旧的serviceType="thirdPartyCall"+serverType="dataBase"格式
     */
    public boolean isDatabaseOperation() {
        // 新格式：直接检查serviceType
        if ("dataBase".equals(serviceType)) {
            return true;
        }
        
        // 旧格式：检查serviceType + serverType组合（向后兼容）
        if ("thirdPartyCall".equals(serviceType) && "dataBase".equals(serverType)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 检查是否为HTTP操作
     */
    public boolean isHttpOperation() {
        return "http".equals(getEffectiveServerType());
    }
    
    /**
     * 检查是否使用ResultSet格式
     */
    public boolean isResultSetFormat() {
        return "resultset".equalsIgnoreCase(getEffectiveResponseFormat());
    }
    
    /**
     * 检查是否使用JSON格式
     */
    public boolean isJsonFormat() {
        return "json".equalsIgnoreCase(getEffectiveResponseFormat());
    }
    
    /**
     * 检查是否为本地处理模式（targetUrl为空）
     */
    public boolean isLocalProcessingMode() {
        return targetUrl == null || targetUrl.trim().isEmpty();
    }
    
    /**
     * 检查是否支持空targetUrl的本地处理
     * 当responseFormat为resultset且为数据库操作时，支持本地处理
     */
    public boolean supportsLocalProcessing() {
        return isDatabaseOperation() && isResultSetFormat();
    }
    
    /**
     * 验证配置的有效性
     */
    public boolean isValidConfiguration() {
        // 基本字段验证
        if (infno == null || infno.trim().isEmpty()) {
            return false;
        }
        if (serviceType == null || serviceType.trim().isEmpty()) {
            return false;
        }
        
        // 根据服务器类型进行不同的验证
        if (isDatabaseOperation()) {
            // 数据库操作验证
            if (dataSource == null || !dataSource.isValidConfiguration()) {
                return false;
            }
            if (sourceQuery == null || sourceQuery.trim().isEmpty()) {
                return false;
            }
            // 支持本地处理模式：当responseFormat为resultset时，targetUrl可以为空
            if (!supportsLocalProcessing() && (targetUrl == null || targetUrl.trim().isEmpty())) {
                return false;
            }
        } else {
            // HTTP操作验证
            if (targetUrl == null || targetUrl.trim().isEmpty()) {
                return false;
            }
            if (method == null || method.trim().isEmpty()) {
                return false;
            }
        }
        
        // 格式验证
        if (requestFormat != null && !requestFormat.trim().isEmpty() && 
            !("JSON".equalsIgnoreCase(requestFormat) || "XML".equalsIgnoreCase(requestFormat))) {
            return false;
        }
        if (responseFormat != null && !responseFormat.trim().isEmpty() && 
            !("JSON".equalsIgnoreCase(responseFormat) || "XML".equalsIgnoreCase(responseFormat) || 
              "json".equalsIgnoreCase(responseFormat) || "resultset".equalsIgnoreCase(responseFormat))) {
            return false;
        }
        if (responseType != null && !responseType.trim().isEmpty() && 
            !("wrapped".equalsIgnoreCase(responseType) || "direct".equalsIgnoreCase(responseType))) {
            return false;
        }
        
        // 服务器类型验证
        if (serverType != null && !serverType.trim().isEmpty() && 
            !("http".equalsIgnoreCase(serverType) || "dataBase".equalsIgnoreCase(serverType))) {
            return false;
        }
        
        return true;
    }
}