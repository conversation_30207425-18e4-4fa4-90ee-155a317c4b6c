package com.my.som.hcs.engine;

import com.my.som.common.exception.AppException;
import com.my.som.common.util.ValidateUtil;
import com.my.som.hcs.constant.HcmIConst;
import com.my.som.hcs.engine.operator.*;
import com.my.som.hcs.engine.operator.api.BaseOperator;
import com.my.som.hcs.engine.param.FindExistOperatorParam;
import com.my.som.hcs.engine.param.FindFrequencyOperatorParam;
import com.my.som.hcs.engine.param.FindDependentExistOperatorParam;
import com.my.som.hcs.engine.param.OperatorBaseParam;
import com.my.som.hcs.engine.vo.*;
import com.my.som.hcs.rule.dto.RuleQueryParamDto;
import com.my.som.hcs.rule.service.RuleConfigManagerService;
import com.my.som.hcs.rule.vo.RuleConfig;
import com.my.som.hcs.rule.vo.RuleMetaDataConfig;
import com.my.som.hcs.util.SnowflakeIdWorker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据验证引擎核心类
 */
@Slf4j
@Component
public class DataValidationEngine {
    private static RuleConfigManagerService ruleConfigManagerService;

    @Autowired
    public void setRuleConfigManagerService(RuleConfigManagerService ruleConfigManagerService) {
        DataValidationEngine.ruleConfigManagerService = ruleConfigManagerService;
    }

    private static Map<String, List<RuleConfig>> ruleConfigMap;


    private static Map<String, List<RuleMetaDataConfig>> ruleMetaDataConfigMap;

    private static Map<String, RuleCacheItemVo> ruleCacheItemVoMap;

    /**
     * 同时存在执行器
     */
    private static FindDoubleExistOperator findDoubleExistOperator;

    @Autowired
    public void setFindDoubleExistOperator(FindDoubleExistOperator findDoubleExistOperator) {
        DataValidationEngine.findDoubleExistOperator = findDoubleExistOperator;
    }

    /**
     * 存在执行器
     */
    private static FindExistOperator findExistOperator;

    @Autowired
    public void setFindExistOperator(FindExistOperator findExistOperator) {
        DataValidationEngine.findExistOperator = findExistOperator;
    }

    /**
     * 依赖存在执行器
     */
    private static FindDependencyExistOperator findDependencyExistOperator;

    @Autowired
    public void setFindDependencyExistOperator(FindDependencyExistOperator findDependencyExistOperator) {
        DataValidationEngine.findDependencyExistOperator = findDependencyExistOperator;
    }


    /**
     * 超频次执行器
     */
    private static FindOverclockTimeOperator findOverclockTimeOperator;

    @Autowired
    public void setFindOverclockTime(FindOverclockTimeOperator findOverclockTimeOperator) {
        DataValidationEngine.findOverclockTimeOperator = findOverclockTimeOperator;
    }

    /**
     * 同频次执行器
     */
    private static FindSameclockTimeOperator findSameclockTimeOperator;

    @Autowired
    public void setFindSameclockTime(FindSameclockTimeOperator findSameclockTimeOperator) {
        DataValidationEngine.findSameclockTimeOperator = findSameclockTimeOperator;
    }

    /**
     * 除外内容间隔操作符
     */
    private static String EXCT_OPERATOR = "#";


    /**
     * 刷新配置
     */
    @PostConstruct
    public static void initConfig() {
        try {
            List<RuleConfig> ruleConfigList = ruleConfigManagerService.queryRuleConfigList(new RuleQueryParamDto());
            if (ValidateUtil.isEmpty(ruleConfigList)) {
                throw new AppException("获取HCM规则失败");
            }
            //todo 构建标准化的规则数据集{"ruleYear":[{key:val}]}
            Map<String, List<RuleConfig>> ruleConfigMap = ruleConfigList.stream().collect(Collectors.groupingBy(RuleConfig::getRuleYear));
            DataValidationEngine.ruleConfigMap = ruleConfigMap;
            List<RuleMetaDataConfig> metaDataConfigList = ruleConfigManagerService.queryMetaDataConfigList(new RuleQueryParamDto());
            if (ValidateUtil.isEmpty(metaDataConfigList)) {
                throw new AppException("获取HCM规则数据源失败");
            }
            //todo 构建标准化的规则数据集{"ruleYear":[{key:val}]}
            Map<String, List<RuleMetaDataConfig>> ruleMetaDataConfigMap = metaDataConfigList.stream().collect(Collectors.groupingBy(RuleMetaDataConfig::getRuleYear));
            DataValidationEngine.ruleMetaDataConfigMap = ruleMetaDataConfigMap;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取HCM数据失败");
        }

        //按年度初始化规则参数
        List<RuleConfig> currRules;
        List<RuleMetaDataConfig> currMetaDataConfigs;
        Map<String, List<RuleMetaDataConfig>> currMetaDataGrpMap;
        Map<String, List<RuleConfig>> ruleByDataGrpMap;
        List<RuleConfig> ruleListByHospList;
        List<RuleConfig> ruleListByDayList;
        List<RuleConfig> ruleListByHourList;
        Map<String, String> metaDataOpraTypeMap;
        Map<String, MetaDataGrpItemPairVo> metaDataGrpSetMap;
        Map<String, ItemFrequencyPairVo> itemFrequencyPairs;
        Map<String, OperatorBaseParam> buildBaseParamMap;
        RuleCacheItemVo ruleCacheItemVo;
        Map<String, RuleCacheItemVo> currRuleCacheItemVoMap = new HashMap<>();
        for (Map.Entry entry : ruleMetaDataConfigMap.entrySet()) {
            // 1、规则列表
            currRules = ruleConfigMap.get(entry.getKey());
            //2、规则数据包(根据对应绑定的rule算子转为对应的入参对象)
            currMetaDataConfigs = ruleMetaDataConfigMap.get(entry.getKey());
            currMetaDataGrpMap = currMetaDataConfigs.stream().collect(Collectors.groupingBy(RuleMetaDataConfig::getDataGrpCode));
            //4、按数据分组(hosp/day)归集规则列表
            ruleByDataGrpMap = currRules.stream().collect(Collectors.groupingBy(RuleConfig::getDataGrp));
            ruleListByHospList = ruleByDataGrpMap.get(HcmIConst.RULE_DATA_GROUP_HOSP);
            ruleListByDayList = ruleByDataGrpMap.get(HcmIConst.RULE_DATA_GROUP_DAY);
            ruleListByHourList = ruleByDataGrpMap.get(HcmIConst.RULE_DATA_GROUP_HOUR);
            //6、获取数据包编码对应的算子类型map
            metaDataOpraTypeMap = currRules.stream().collect(Collectors.toMap(RuleConfig::getRuleDataMeta, RuleConfig::getOpraType, (oldVal, newVal) -> newVal)); // 假设没有重复的组合键名冲突问题（例如Alice_NY和Bob_NY）
            //7、规则数据包中的每个数据包中的数据对
            metaDataGrpSetMap = getSetByMetaDataGrpByList(currMetaDataGrpMap);
            //8、构建超限数据对
            itemFrequencyPairs = getSetItemPairByMetaDataGrpList(currMetaDataGrpMap, metaDataOpraTypeMap);
            //9、构建每个数据包对应的入参对象
            buildBaseParamMap = generateMetaDataParamByOpraType(currRules, metaDataOpraTypeMap, metaDataGrpSetMap, itemFrequencyPairs);
            ruleCacheItemVo = new RuleCacheItemVo();
            ruleCacheItemVo.setBuildBaseParamMap(buildBaseParamMap);
            ruleCacheItemVo.setRuleListByDayList(ruleListByDayList);
            ruleCacheItemVo.setRuleListByHospList(ruleListByHospList);
            ruleCacheItemVo.setRuleListByHourList(ruleListByHourList);
            currRuleCacheItemVoMap.put((String) entry.getKey(), ruleCacheItemVo);
        }
        ruleCacheItemVoMap = currRuleCacheItemVoMap;
    }


    /**
     * 验证数据
     *
     * @param validMedDataInfo 待验证的数据
     * @return 验证结果
     */
    public ValidationResultVo validate(ValidMedDataInfo validMedDataInfo) {
        ValidationResultVo result = new ValidationResultVo();
        result.setValidStatus(true);
        if (ValidateUtil.isEmpty(ruleConfigMap) || ValidateUtil.isEmpty(ruleMetaDataConfigMap)) {
            return result;
        }
        //获取执行年度
//        if (ValidateUtil.isEmpty(validMedDataInfo.getYm())) {
//            //获取当前最新年度的质控规则执行
//            validMedDataInfo.setYm("" + Calendar.getInstance().get(Calendar.YEAR));
//        }
        //获取执行年度，设置25年规则
        validMedDataInfo.setYm("" + Calendar.getInstance().get(Calendar.YEAR));

        //涉及多线程调用，使用并发集合
        List<ValidateDetailResultVo> validateDetailResultVoList = Collections.synchronizedList(new ArrayList<>());
        //1、获取校验参数对象
        RuleCacheItemVo ruleCacheItemVo = ruleCacheItemVoMap.get(validMedDataInfo.getYm());

        //2.1、生成按天收费明细归集
        if (ValidateUtil.isNotEmpty(validMedDataInfo.getMedFeeItemInfos())) {
            validMedDataInfo.setMedFeeItemInfoByDays(validMedDataInfo.getMedFeeItemInfos().stream().collect(Collectors.groupingBy(MedFeeItemInfoVo::getFeeOcurDayTime)));
        }

        //2.2、生成按时收费明细归集
        if (ValidateUtil.isNotEmpty(validMedDataInfo.getMedFeeItemInfos())) {
            validMedDataInfo.setMedFeeItemInfoByHours(validMedDataInfo.getMedFeeItemInfos().stream().collect(Collectors.groupingBy(MedFeeItemInfoVo::getFeeOcurTime)));
        }

        //3、标准公共参数
        OperatorBaseParam baseHospParam = new OperatorBaseParam();
        //默认数据为一次住院
        List<MedFeeItemInfoVo> currMedFeeItemInfos = validMedDataInfo.getMedFeeItemInfos();
        List<ValidateBaseResult> validateResultHosp;

        //4、优先执行一次住院数据分组对应规则
        baseHospParam.setTargetSet(getSetByMedFeeList(currMedFeeItemInfos));
        // 项目费用
        Map<String, BigDecimal> medListCodgFeeMapHosp = getMapByMedFeeList(currMedFeeItemInfos);
        baseHospParam.setMedListCodgFeeMap(medListCodgFeeMapHosp);
        // 设置除外诊断内容
        baseHospParam.setMedDiagInfoStr(validMedDataInfo.getMedDiagInfoStr());
        for (RuleConfig rule : ruleCacheItemVo.getRuleListByHospList()) {
            validateResultHosp = validateRule(rule, baseHospParam, ruleCacheItemVo.getBuildBaseParamMap(), currMedFeeItemInfos);
            if (ValidateUtil.isNotEmpty(validateResultHosp)) {
                //根据错误明细ID获取对应的违规明细费用
                //存在质控结果
                validateDetailResultVoList.addAll(generateValidResultList(validateResultHosp, rule, medListCodgFeeMapHosp, validMedDataInfo));
            }
        }

        //5、优先按天数据分组对应规则
        OperatorBaseParam baseDayParam = new OperatorBaseParam();
        Map<String, List<MedFeeItemInfoVo>> medFeeItemInfoByDayMap = validMedDataInfo.getMedFeeItemInfoByDays();
        //按天循
        List<ValidateBaseResult> validateResultDay;
        // 项目费用
        Map<String, BigDecimal> medListCodgFeeMapDay;
        // 设置除外诊断内容
        baseDayParam.setMedDiagInfoStr(validMedDataInfo.getMedDiagInfoStr());
        for (Map.Entry entry : medFeeItemInfoByDayMap.entrySet()) {
            currMedFeeItemInfos = (List<MedFeeItemInfoVo>) entry.getValue();
            baseDayParam.setTargetSet(getSetByMedFeeList(currMedFeeItemInfos));
            medListCodgFeeMapDay = getMapByMedFeeList(currMedFeeItemInfos);
            baseDayParam.setMedListCodgFeeMap(medListCodgFeeMapDay);
            //2、处理按天数据分组对应规则
            for (RuleConfig rule : ruleCacheItemVo.getRuleListByDayList()) {
                validateResultDay = validateRule(rule, baseDayParam, ruleCacheItemVo.getBuildBaseParamMap(), currMedFeeItemInfos);
                if (ValidateUtil.isNotEmpty(validateResultDay)) {
                    //存在质控结果
                    validateDetailResultVoList.addAll(generateValidResultList(validateResultDay, rule, medListCodgFeeMapDay, validMedDataInfo));
                }
            }
        }

        //6、优先按小时数据分组对应规则
        OperatorBaseParam baseHourParam = new OperatorBaseParam();
        Map<String, List<MedFeeItemInfoVo>> medFeeItemInfoByHourMap = validMedDataInfo.getMedFeeItemInfoByHours();
        //按小时循环
        List<ValidateBaseResult> validateResultHour;
        // 项目费用
        // 设置除外诊断内容
        baseHourParam.setMedDiagInfoStr(validMedDataInfo.getMedDiagInfoStr());
        Map<String, BigDecimal> medListCodgFeeMapHour;
        for (Map.Entry entry : medFeeItemInfoByHourMap.entrySet()) {
            currMedFeeItemInfos = (List<MedFeeItemInfoVo>) entry.getValue();
            baseHourParam.setTargetSet(getSetByMedFeeList(currMedFeeItemInfos));
            medListCodgFeeMapHour = getMapByMedFeeList(currMedFeeItemInfos);
            baseHourParam.setMedListCodgFeeMap(medListCodgFeeMapHour);
            //2、处理按时数据分组对应规则
            for (RuleConfig rule : ruleCacheItemVo.getRuleListByHourList()) {
                validateResultHour = validateRule(rule, baseHourParam, ruleCacheItemVo.getBuildBaseParamMap(), currMedFeeItemInfos);
                if (ValidateUtil.isNotEmpty(validateResultHour)) {
                    //存在质控结果
                    validateDetailResultVoList.addAll(generateValidResultList(validateResultHour, rule, medListCodgFeeMapHour, validMedDataInfo));
                }
            }
        }

        //返回异常数据
        result.setValidateBaseErrors(validateDetailResultVoList);
        return result;
    }

    private static List<ValidateDetailResultVo> generateValidResultList(List<ValidateBaseResult> validateBaseResultList, RuleConfig ruleConfig, Map<String, BigDecimal> medListCodgFeeMapHosp, ValidMedDataInfo validMedDataInfo) {
        List<ValidateDetailResultVo> validateDetailResultVoList = new ArrayList<>();
        String ruleOpraType;
        String uniqueErrorDetailCodg;
        for (ValidateBaseResult validateBaseResult : validateBaseResultList) {
            ruleOpraType = ruleConfig.getOpraType();
            uniqueErrorDetailCodg = SnowflakeIdWorker.generateId().toString();
            switch (ruleOpraType) {
                case "DoubleExistOperator":
                    ValidateDetailResultVo validateDetailResultVoItemMate1 = new ValidateDetailResultVo();
                    validateDetailResultVoItemMate1.setUnique_id(validMedDataInfo.getUniqueId());
                    validateDetailResultVoItemMate1.setRule_detl_codg(ruleConfig.getRuleDetlCodg());
                    validateDetailResultVoItemMate1.setError_detail_codg(uniqueErrorDetailCodg);
                    validateDetailResultVoItemMate1.setMed_list_codg((String) validateBaseResult.getItemMate1());
                    validateDetailResultVoItemMate1.setRule_data_meta(ruleConfig.getRuleDataMeta());
                    validateDetailResultVoItemMate1.setError_type(ruleConfig.getRuleTypeCodg());
                    validateDetailResultVoItemMate1.setError_desc(ruleConfig.getRuleTypeName());
                    validateDetailResultVoItemMate1.setVola_deg(ruleConfig.getVolaDeg());
                    //设置项目名称
                    validateDetailResultVoItemMate1.setMed_list_name("");
                    // G1按0计入
                    validateDetailResultVoItemMate1.setViolation_amount(BigDecimal.ZERO);
                    validateDetailResultVoItemMate1.setRule_scen_type(validMedDataInfo.getDataType());
                    validateDetailResultVoList.add(validateDetailResultVoItemMate1);

                    ValidateDetailResultVo validateDetailResultVoItemMate2 = new ValidateDetailResultVo();
                    validateDetailResultVoItemMate2.setUnique_id(validMedDataInfo.getUniqueId());
                    validateDetailResultVoItemMate2.setRule_detl_codg(ruleConfig.getRuleDetlCodg());
                    validateDetailResultVoItemMate2.setError_detail_codg(uniqueErrorDetailCodg);
                    validateDetailResultVoItemMate2.setMed_list_codg((String) validateBaseResult.getItemMate2());
                    validateDetailResultVoItemMate2.setRule_data_meta(ruleConfig.getRuleDataMeta());
                    validateDetailResultVoItemMate2.setError_type(ruleConfig.getRuleTypeCodg());
                    validateDetailResultVoItemMate2.setError_desc(ruleConfig.getRuleTypeName());
                    validateDetailResultVoItemMate2.setVola_deg(ruleConfig.getVolaDeg());
                    //设置项目名称
                    validateDetailResultVoItemMate2.setMed_list_name("");
                    // 同时存在时只计入G2费用
                    validateDetailResultVoItemMate2.setViolation_amount(medListCodgFeeMapHosp.getOrDefault(validateBaseResult.getItemMate2(), BigDecimal.ZERO));
                    validateDetailResultVoItemMate2.setRule_scen_type(validMedDataInfo.getDataType());
                    validateDetailResultVoList.add(validateDetailResultVoItemMate2);
                    break;
                case "ExistOperator":
                    ValidateDetailResultVo validateDetailResultVoItemMate3 = new ValidateDetailResultVo();
                    validateDetailResultVoItemMate3.setUnique_id(validMedDataInfo.getUniqueId());
                    validateDetailResultVoItemMate3.setRule_detl_codg(ruleConfig.getRuleDetlCodg());
                    validateDetailResultVoItemMate3.setError_detail_codg(uniqueErrorDetailCodg);
                    validateDetailResultVoItemMate3.setMed_list_codg((String) validateBaseResult.getItemMate1());
                    validateDetailResultVoItemMate3.setRule_data_meta(ruleConfig.getRuleDataMeta());
                    validateDetailResultVoItemMate3.setError_type(ruleConfig.getRuleTypeCodg());
                    validateDetailResultVoItemMate3.setError_desc(ruleConfig.getRuleTypeName());
                    validateDetailResultVoItemMate3.setRule_scen_type(validMedDataInfo.getDataType());
                    validateDetailResultVoItemMate3.setVola_deg(ruleConfig.getVolaDeg());
                    //设置项目名称
                    validateDetailResultVoItemMate3.setMed_list_name("");
                    validateDetailResultVoItemMate3.setViolation_amount(medListCodgFeeMapHosp.getOrDefault(validateBaseResult.getItemMate1(), BigDecimal.ZERO));
                    validateDetailResultVoList.add(validateDetailResultVoItemMate3);
                    break;
                case "OverclockTime":
                    ValidateDetailResultVo validateDetailResultVoItemMate4 = new ValidateDetailResultVo();
                    validateDetailResultVoItemMate4.setUnique_id(validMedDataInfo.getUniqueId());
                    validateDetailResultVoItemMate4.setRule_detl_codg(ruleConfig.getRuleDetlCodg());
                    validateDetailResultVoItemMate4.setError_detail_codg(uniqueErrorDetailCodg);
                    validateDetailResultVoItemMate4.setMed_list_codg((String) validateBaseResult.getItemMate1());
                    validateDetailResultVoItemMate4.setRule_data_meta(ruleConfig.getRuleDataMeta());
                    validateDetailResultVoItemMate4.setError_type(ruleConfig.getRuleTypeCodg());
                    validateDetailResultVoItemMate4.setError_desc(ruleConfig.getRuleTypeName());
                    validateDetailResultVoItemMate4.setRule_scen_type(validMedDataInfo.getDataType());
                    validateDetailResultVoItemMate4.setVola_deg(ruleConfig.getVolaDeg());
                    //设置项目名称
                    validateDetailResultVoItemMate4.setMed_list_name("");
                    validateDetailResultVoItemMate4.setViolation_amount(medListCodgFeeMapHosp.getOrDefault(validateBaseResult.getItemMate1(), BigDecimal.ZERO));
                    validateDetailResultVoItemMate4.setCnt(((ValidOverclockTimeResultVo) validateBaseResult).getCountSize());
                    validateDetailResultVoList.add(validateDetailResultVoItemMate4);
                    break;

                case "FindDependencyExist":
                    ValidateDetailResultVo validateDetailResultVoItemMate5 = new ValidateDetailResultVo();
                    validateDetailResultVoItemMate5.setUnique_id(validMedDataInfo.getUniqueId());
                    validateDetailResultVoItemMate5.setRule_detl_codg(ruleConfig.getRuleDetlCodg());
                    validateDetailResultVoItemMate5.setError_detail_codg(uniqueErrorDetailCodg);
                    validateDetailResultVoItemMate5.setMed_list_codg((String) validateBaseResult.getItemMate1());
                    validateDetailResultVoItemMate5.setRule_data_meta(ruleConfig.getRuleDataMeta());
                    validateDetailResultVoItemMate5.setError_type(ruleConfig.getRuleTypeCodg());
                    validateDetailResultVoItemMate5.setError_desc(ruleConfig.getRuleTypeName());
                    validateDetailResultVoItemMate5.setVola_deg(ruleConfig.getVolaDeg());
                    //设置项目名称
                    validateDetailResultVoItemMate5.setMed_list_name("");
                    validateDetailResultVoItemMate5.setViolation_amount(medListCodgFeeMapHosp.getOrDefault(validateBaseResult.getItemMate1(), BigDecimal.ZERO));
                    validateDetailResultVoItemMate5.setRule_scen_type(validMedDataInfo.getDataType());
                    validateDetailResultVoList.add(validateDetailResultVoItemMate5);
                    break;

                case "SameclockTime":
                    ValidateDetailResultVo validateDetailResultVoItemMate7 = new ValidateDetailResultVo();
                    validateDetailResultVoItemMate7.setUnique_id(validMedDataInfo.getUniqueId());
                    validateDetailResultVoItemMate7.setRule_detl_codg(ruleConfig.getRuleDetlCodg());
                    validateDetailResultVoItemMate7.setError_detail_codg(uniqueErrorDetailCodg);
                    validateDetailResultVoItemMate7.setMed_list_codg((String) validateBaseResult.getItemMate1());
                    validateDetailResultVoItemMate7.setRule_data_meta(ruleConfig.getRuleDataMeta());
                    validateDetailResultVoItemMate7.setError_type(ruleConfig.getRuleTypeCodg());
                    validateDetailResultVoItemMate7.setError_desc(ruleConfig.getRuleTypeName());
                    validateDetailResultVoItemMate7.setRule_scen_type(validMedDataInfo.getDataType());
                    validateDetailResultVoItemMate7.setVola_deg(ruleConfig.getVolaDeg());
                    //设置项目名称
                    validateDetailResultVoItemMate7.setMed_list_name("");
                    validateDetailResultVoItemMate7.setViolation_amount(medListCodgFeeMapHosp.getOrDefault(validateBaseResult.getItemMate1(), BigDecimal.ZERO));
                    validateDetailResultVoItemMate7.setCnt(((ValidOverclockTimeResultVo) validateBaseResult).getCountSize());
                    validateDetailResultVoList.add(validateDetailResultVoItemMate7);
                    break;
                default:
                    throw new AppException("此算子未适配" + ruleOpraType);
            }
        }
        return validateDetailResultVoList;
    }

    private static Map<String, OperatorBaseParam> generateMetaDataParamByOpraType(List<RuleConfig> currRules, Map<String, String> metaDataOpraTypeMap, Map<String, MetaDataGrpItemPairVo> metaDataGrpSetMap, Map<String, ItemFrequencyPairVo> itemFrequencyPairs) {
        Map<String, OperatorBaseParam> resultBaseParamMap = new HashMap<>();
        String ruleOpraType;
        String ruleDataMeta;
        MetaDataGrpItemPairVo metaDataGrpItemPairVo;
        ItemFrequencyPairVo itemFrequencyPairVo;
        for (RuleConfig rule : currRules) {
            ruleDataMeta = rule.getRuleDataMeta();
            ruleOpraType = metaDataOpraTypeMap.get(ruleDataMeta);
            metaDataGrpItemPairVo = metaDataGrpSetMap.get(ruleDataMeta);
            itemFrequencyPairVo = itemFrequencyPairs.get(ruleDataMeta);
            switch (ruleOpraType) {
                case "DoubleExistOperator":
                    //同时存在时检出
                    FindExistOperatorParam findExistOperatorParam = new FindExistOperatorParam();
                    findExistOperatorParam.setMetaSet1(metaDataGrpItemPairVo.getMetaGrp1());
                    findExistOperatorParam.setMetaSet2(metaDataGrpItemPairVo.getMetaGrp2());
                    if (ValidateUtil.isNotEmpty(rule.getExctType())) {
                        findExistOperatorParam.setExctType(rule.getExctType());
                        findExistOperatorParam.setExctContArray(rule.getExctCont().split(EXCT_OPERATOR));
                    }
                    resultBaseParamMap.put(ruleDataMeta, findExistOperatorParam);
                    break;
                case "ExistOperator":
                    //存在时检出
                    FindExistOperatorParam findExistOperatorParam1 = new FindExistOperatorParam();
                    findExistOperatorParam1.setMetaSet1(metaDataGrpItemPairVo.getMetaGrp1());
                    findExistOperatorParam1.setMetaSet2(metaDataGrpItemPairVo.getMetaGrp2());
                    if (ValidateUtil.isNotEmpty(rule.getExctType())) {
                        findExistOperatorParam1.setExctType(rule.getExctType());
                        findExistOperatorParam1.setExctContArray(rule.getExctCont().split(EXCT_OPERATOR));
                    }
                    resultBaseParamMap.put(ruleDataMeta, findExistOperatorParam1);
                    break;
                case "OverclockTime":
                    //超长阈值时检出
                    FindFrequencyOperatorParam findFrequencyOperatorParam = new FindFrequencyOperatorParam();
                    findFrequencyOperatorParam.setMetaSet1(metaDataGrpItemPairVo.getMetaGrp1());
                    findFrequencyOperatorParam.setMetaSet2(metaDataGrpItemPairVo.getMetaGrp2());
                    findFrequencyOperatorParam.setMetaFrequencySet(new HashSet<>(Arrays.asList(itemFrequencyPairVo)));
                    if (ValidateUtil.isNotEmpty(rule.getExctType())) {
                        findFrequencyOperatorParam.setExctType(rule.getExctType());
                        findFrequencyOperatorParam.setExctContArray(rule.getExctCont().split(EXCT_OPERATOR));
                    }
                    resultBaseParamMap.put(ruleDataMeta, findFrequencyOperatorParam);
                    break;
                case "FindDependencyExist":
                    //不依赖存在时检出
                    FindDependentExistOperatorParam findDependentExistOperatorParam = new FindDependentExistOperatorParam();
                    findDependentExistOperatorParam.setMetaSet1(metaDataGrpItemPairVo.getMetaGrp1());
                    findDependentExistOperatorParam.setMetaSet2(metaDataGrpItemPairVo.getMetaGrp2());
                    if (ValidateUtil.isNotEmpty(rule.getExctType())) {
                        findDependentExistOperatorParam.setExctType(rule.getExctType());
                        findDependentExistOperatorParam.setExctContArray(rule.getExctCont().split(EXCT_OPERATOR));
                    }
                    resultBaseParamMap.put(ruleDataMeta, findDependentExistOperatorParam);
                    break;
                case "SameclockTime":
                    //等于阈值时检出
                    FindFrequencyOperatorParam findSameOverclockTimeOprnParam = new FindFrequencyOperatorParam();
                    findSameOverclockTimeOprnParam.setMetaSet1(metaDataGrpItemPairVo.getMetaGrp1());
                    findSameOverclockTimeOprnParam.setMetaSet2(metaDataGrpItemPairVo.getMetaGrp2());
                    findSameOverclockTimeOprnParam.setMetaFrequencySet(new HashSet<>(Arrays.asList(itemFrequencyPairVo)));
                    if (ValidateUtil.isNotEmpty(rule.getExctType())) {
                        findSameOverclockTimeOprnParam.setExctType(rule.getExctType());
                        findSameOverclockTimeOprnParam.setExctContArray(rule.getExctCont().split(EXCT_OPERATOR));
                    }
                    resultBaseParamMap.put(ruleDataMeta, findSameOverclockTimeOprnParam);
                    break;
                default:
                    throw new AppException("此算子未适配" + rule.getOpraType());
            }
        }
        return resultBaseParamMap;
    }

    /**
     * 执行具体规则校验
     *
     * @param rule
     * @param operatorBaseParam
     * @param buildBaseParamMap
     * @param medFeeItemInfos
     * @return
     */
    private List<ValidateBaseResult> validateRule(RuleConfig rule, OperatorBaseParam operatorBaseParam, Map<String, OperatorBaseParam> buildBaseParamMap, List<MedFeeItemInfoVo> medFeeItemInfos) {
        List<ValidateBaseResult> resultList = new ArrayList<>();
        switch (rule.getOpraType()) {
            case "DoubleExistOperator":
                FindExistOperatorParam findExistOperatorParam = new FindExistOperatorParam();
                findExistOperatorParam.setMetaSet1(buildBaseParamMap.get(rule.getRuleDataMeta()).getMetaSet1());
                findExistOperatorParam.setMetaSet2(buildBaseParamMap.get(rule.getRuleDataMeta()).getMetaSet2());
                findExistOperatorParam.setTargetSet(operatorBaseParam.getTargetSet());
                findExistOperatorParam.setMedListCodgFeeMap(operatorBaseParam.getMedListCodgFeeMap());
                // 新增除外类型和除外内容
                findExistOperatorParam.setExctType(buildBaseParamMap.get(rule.getRuleDataMeta()).getExctType());
                findExistOperatorParam.setExctContArray(buildBaseParamMap.get(rule.getRuleDataMeta()).getExctContArray());
                findExistOperatorParam.setMedDiagInfoStr(operatorBaseParam.getMedDiagInfoStr());
                resultList = validateDoubleExist(findDoubleExistOperator, findExistOperatorParam);
                findExistOperatorParam = null;
                break;
            case "ExistOperator":
                FindExistOperatorParam findExistOperatorParam1 = new FindExistOperatorParam();
                findExistOperatorParam1.setMetaSet1(buildBaseParamMap.get(rule.getRuleDataMeta()).getMetaSet1());
                findExistOperatorParam1.setMetaSet2(buildBaseParamMap.get(rule.getRuleDataMeta()).getMetaSet2());
                findExistOperatorParam1.setTargetSet(operatorBaseParam.getTargetSet());
                findExistOperatorParam1.setMedListCodgFeeMap(operatorBaseParam.getMedListCodgFeeMap());
                // 新增除外类型和除外内容
                findExistOperatorParam1.setExctType(buildBaseParamMap.get(rule.getRuleDataMeta()).getExctType());
                findExistOperatorParam1.setExctContArray(buildBaseParamMap.get(rule.getRuleDataMeta()).getExctContArray());
                findExistOperatorParam1.setMedDiagInfoStr(operatorBaseParam.getMedDiagInfoStr());
                resultList = validateDoubleExist(findExistOperator, findExistOperatorParam1);
                findExistOperatorParam1 = null;
                break;
            case "OverclockTime":
                FindFrequencyOperatorParam findFrequencyOperatorParam = new FindFrequencyOperatorParam();
                findFrequencyOperatorParam.setMetaSet1(buildBaseParamMap.get(rule.getRuleDataMeta()).getMetaSet1());
                findFrequencyOperatorParam.setMetaSet2(buildBaseParamMap.get(rule.getRuleDataMeta()).getMetaSet2());
                findFrequencyOperatorParam.setMetaFrequencySet(((FindFrequencyOperatorParam) buildBaseParamMap.get(rule.getRuleDataMeta())).getMetaFrequencySet());
                findFrequencyOperatorParam.setTargetSet(operatorBaseParam.getTargetSet());
                findFrequencyOperatorParam.setTargetItemSet(getItemCountSetByMedFeeList(medFeeItemInfos));
                findFrequencyOperatorParam.setMedListCodgFeeMap(operatorBaseParam.getMedListCodgFeeMap());
                // 新增除外类型和除外内容
                findFrequencyOperatorParam.setExctType(buildBaseParamMap.get(rule.getRuleDataMeta()).getExctType());
                findFrequencyOperatorParam.setExctContArray(buildBaseParamMap.get(rule.getRuleDataMeta()).getExctContArray());
                findFrequencyOperatorParam.setMedDiagInfoStr(operatorBaseParam.getMedDiagInfoStr());
                resultList = validateDoubleExist(findOverclockTimeOperator, findFrequencyOperatorParam);
                findFrequencyOperatorParam = null;
                break;
            case "FindDependencyExist":
                FindDependentExistOperatorParam findDependentExistOperatorParam = new FindDependentExistOperatorParam();
                findDependentExistOperatorParam.setMetaSet1(buildBaseParamMap.get(rule.getRuleDataMeta()).getMetaSet1());
                findDependentExistOperatorParam.setMetaSet2(buildBaseParamMap.get(rule.getRuleDataMeta()).getMetaSet2());
                findDependentExistOperatorParam.setTargetSet(operatorBaseParam.getTargetSet());
                findDependentExistOperatorParam.setMedListCodgFeeMap(operatorBaseParam.getMedListCodgFeeMap());
                // 新增除外类型和除外内容
                findDependentExistOperatorParam.setExctType(buildBaseParamMap.get(rule.getRuleDataMeta()).getExctType());
                findDependentExistOperatorParam.setExctContArray(buildBaseParamMap.get(rule.getRuleDataMeta()).getExctContArray());
                findDependentExistOperatorParam.setMedDiagInfoStr(operatorBaseParam.getMedDiagInfoStr());
                resultList = validateDoubleExist(findDependencyExistOperator, findDependentExistOperatorParam);
                findDependentExistOperatorParam = null;
                break;
            case "SameclockTime":
                FindFrequencyOperatorParam findSameOverclockTimeOperatorParam = new FindFrequencyOperatorParam();
                findSameOverclockTimeOperatorParam.setMetaFrequencySet(((FindFrequencyOperatorParam) buildBaseParamMap.get(rule.getRuleDataMeta())).getMetaFrequencySet());
                findSameOverclockTimeOperatorParam.setMetaSet1(buildBaseParamMap.get(rule.getRuleDataMeta()).getMetaSet1());
                findSameOverclockTimeOperatorParam.setMetaSet2(buildBaseParamMap.get(rule.getRuleDataMeta()).getMetaSet2());
                findSameOverclockTimeOperatorParam.setTargetSet(operatorBaseParam.getTargetSet());
                findSameOverclockTimeOperatorParam.setTargetItemSet(getItemCountSetByMedFeeList(medFeeItemInfos));
                findSameOverclockTimeOperatorParam.setMedListCodgFeeMap(operatorBaseParam.getMedListCodgFeeMap());
                // 新增除外类型和除外内容
                findSameOverclockTimeOperatorParam.setExctType(buildBaseParamMap.get(rule.getRuleDataMeta()).getExctType());
                findSameOverclockTimeOperatorParam.setExctContArray(buildBaseParamMap.get(rule.getRuleDataMeta()).getExctContArray());
                findSameOverclockTimeOperatorParam.setMedDiagInfoStr(operatorBaseParam.getMedDiagInfoStr());
                resultList = validateDoubleExist(findSameclockTimeOperator, findSameOverclockTimeOperatorParam);
                findSameOverclockTimeOperatorParam = null;
                break;
            default:
                throw new AppException("此算子未适配" + rule.getOpraType());
        }
        return resultList;
    }

    /**
     * 执行同时存在逻辑判断
     *
     * @param baseOperator
     * @param operatorBaseParam
     * @return
     */
    private List<ValidateBaseResult> validateDoubleExist(BaseOperator baseOperator, OperatorBaseParam operatorBaseParam) {
        // 执行校验
        return baseOperator.excute(operatorBaseParam);
    }

    /**
     * 获取校验原始数据set对象
     *
     * @param medFeeItemInfos
     * @return
     */
    private Set<?> getSetByMedFeeList(List<MedFeeItemInfoVo> medFeeItemInfos) {
        HashSet<String> resultSet = new HashSet<>();
        for (MedFeeItemInfoVo medFeeItemInfo :
                medFeeItemInfos) {
            resultSet.add(medFeeItemInfo.getMedListCodg());
        }
        return resultSet;
    }

    /**
     * 获取原始数据Map对象<[编码,费用]>
     *
     * @param medFeeItemInfos
     * @return
     */
    private Map<String, BigDecimal> getMapByMedFeeList(List<MedFeeItemInfoVo> medFeeItemInfos) {
        Map<String, BigDecimal> resultMap = new HashMap<>();
        BigDecimal itemFee;
        for (MedFeeItemInfoVo medFeeItemInfo :
                medFeeItemInfos) {
            itemFee = resultMap.get(medFeeItemInfo.getMedListCodg());
            if (!ValidateUtil.isEmpty(itemFee)) {
                resultMap.put(medFeeItemInfo.getMedListCodg(), itemFee.add(medFeeItemInfo.getItemFeeSumamt()));
            } else {
                resultMap.put(medFeeItemInfo.getMedListCodg(), medFeeItemInfo.getItemFeeSumamt());
            }
        }
        return resultMap;
    }

    /**
     * 获取超频校验原始数据set对象
     * List<ItemCodeCount> targetItemSet
     *
     * @param medFeeItemInfos
     * @return
     */
    private List<ItemCodeCountVo> getItemCountSetByMedFeeList(List<MedFeeItemInfoVo> medFeeItemInfos) {

        //可能会涉及到相同项目不同天，需要累计
        List<ItemCodeCountVo> resultSet = new ArrayList<>();
        ItemCodeCountVo itemCodeCountVo;

        Map<String, BigDecimal> resultMap = new HashMap<>();
        BigDecimal countSize;
        for (MedFeeItemInfoVo medFeeItemInfo :
                medFeeItemInfos) {
            countSize = resultMap.get(medFeeItemInfo.getMedListCodg());
            if (!ValidateUtil.isEmpty(countSize)) {
                resultMap.put(medFeeItemInfo.getMedListCodg(), countSize.add(medFeeItemInfo.getCountSize()));
            } else {
                resultMap.put(medFeeItemInfo.getMedListCodg(), medFeeItemInfo.getCountSize());
            }
        }

        for (Map.Entry<String, BigDecimal> entry : resultMap.entrySet()) {
            itemCodeCountVo = new ItemCodeCountVo();
            itemCodeCountVo.setItemCode(entry.getKey());
            itemCodeCountVo.setCountSize(entry.getValue());
            resultSet.add(itemCodeCountVo);
        }


        return resultSet;
    }


    /**
     * 获取规则数据字典数据map对象(key=数据包编码，val=数据包对)
     *
     * @param ruleMetaDataConfigMap
     * @return
     */
    private static Map<String, MetaDataGrpItemPairVo> getSetByMetaDataGrpByList(Map<String, List<RuleMetaDataConfig>> ruleMetaDataConfigMap) {
        Map<String, MetaDataGrpItemPairVo> resultMetaDataConfigMap = new HashMap<>();
        MetaDataGrpItemPairVo metaDataGrpItemPairVo;
        for (Map.Entry entry :
                ruleMetaDataConfigMap.entrySet()) {
            metaDataGrpItemPairVo = getSetByMetaDataGrpByDataGrp((List<RuleMetaDataConfig>) entry.getValue());
            resultMetaDataConfigMap.put((String) entry.getKey(), metaDataGrpItemPairVo);
        }
        return resultMetaDataConfigMap;
    }

    /**
     * 获取规则数据字典数据对
     *
     * @param ruleMetaDataConfigs
     * @return
     */
    private static MetaDataGrpItemPairVo getSetByMetaDataGrpByDataGrp(List<RuleMetaDataConfig> ruleMetaDataConfigs) {
        Map<String, HashSet<String>> resultMetaDataConfigMap = new HashMap<>();
        HashSet<String> resultSet;
        for (RuleMetaDataConfig metaDataConfig :
                ruleMetaDataConfigs) {
            resultSet = resultMetaDataConfigMap.get(metaDataConfig.getDataDetailCode());
            if (!ValidateUtil.isEmpty(resultSet)) {
                resultSet.add(metaDataConfig.getDataCode());
            } else {
                resultSet = new HashSet<>();
                resultSet.add(metaDataConfig.getDataCode());
            }
            resultMetaDataConfigMap.put(metaDataConfig.getDataDetailCode(), resultSet);
        }
        MetaDataGrpItemPairVo metaDataGrpItemPairVo = new MetaDataGrpItemPairVo();
        metaDataGrpItemPairVo.setMetaGrp1(resultMetaDataConfigMap.get(HcmIConst.META_DATA_GROUP_TYPE_G1));
        metaDataGrpItemPairVo.setMetaGrp2(resultMetaDataConfigMap.get(HcmIConst.META_DATA_GROUP_TYPE_G2));
        return metaDataGrpItemPairVo;
    }


    /**
     * 获取规则数据字典数据SET<ItemFrequencyPair>对象
     *
     * @param ruleMetaDataConfigMap
     * @return
     */
    private static Map<String, ItemFrequencyPairVo> getSetItemPairByMetaDataGrpList(Map<String, List<RuleMetaDataConfig>> ruleMetaDataConfigMap, Map<String, String> metaDataOpraTypeMap) {
        HashMap<String, ItemFrequencyPairVo> itemPairMap = new HashMap<>();
        List<RuleMetaDataConfig> itemMetaGrpList;
        List<String> frequencyPairOperator = new ArrayList<>(Arrays.asList(HcmIConst.RULE_OPRA_TYPE_OVER_TIME, HcmIConst.RULE_OPRA_TYPE_SAME_TIME));
        for (Map.Entry entry :
                ruleMetaDataConfigMap.entrySet()) {
            if (!frequencyPairOperator.contains(metaDataOpraTypeMap.get(entry.getKey()))) {
                // 非超限或者同限制类规则，不需要计算项目超频次对象
                continue;
            }
            itemMetaGrpList = (List<RuleMetaDataConfig>) entry.getValue();
            ItemFrequencyPairVo pair = new ItemFrequencyPairVo();
            for (RuleMetaDataConfig metaDataConfig :
                    itemMetaGrpList) {
                if (HcmIConst.META_DATA_GROUP_TYPE_G1.equals(metaDataConfig.getDataDetailCode())) {
                    pair.setItemCode(metaDataConfig.getDataCode());
                } else if (HcmIConst.META_DATA_GROUP_TYPE_G2.equals(metaDataConfig.getDataDetailCode())) {
                    pair.setThreshold(new BigDecimal(metaDataConfig.getDataCode()));
                }
            }
            itemPairMap.put((String) entry.getKey(), pair);
        }
        return itemPairMap;
    }
}