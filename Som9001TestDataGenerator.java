package com.my.som.test;

import com.my.som.dts.entity.param.Som9001Param;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Som9001接口测试数据生成器
 */
public class Som9001TestDataGenerator {

    /**
     * 生成住院患者测试数据（DRG分组）
     */
    public static Som9001Param generateInpatientTestData() {
        Som9001Param param = new Som9001Param();
        
        // 基本信息
        Som9001Param.baseinfo baseinfo = new Som9001Param.baseinfo();
        baseinfo.setFixmedins_code("H12345678901");
        baseinfo.setMedfee_paymtd_code("1"); // 医保支付
        baseinfo.setPatn_ipt_cnt("1");
        baseinfo.setMedcasno("ZY20250812001");
        baseinfo.setPsn_name("张三");
        baseinfo.setGend("1"); // 男性
        baseinfo.setBrdy("1980-05-15");
        baseinfo.setAge("44");
        baseinfo.setCertno("510100198005150001");
        baseinfo.setAdm_way_code("1"); // 急诊
        baseinfo.setAdm_date("2025-08-01 09:30:00");
        baseinfo.setDscg_date("2025-08-07 15:20:00");
        baseinfo.setIpt_days("6"); // 住院6天
        baseinfo.setMedfee_sumamt("25000.50");
        baseinfo.setInsuplc("510100"); // 参保地
        
        // 设置各项费用
        baseinfo.setOrdn_med_servfee("1200.00");
        baseinfo.setOrdn_trt_oprt_fee("800.00");
        baseinfo.setNurs_fee("600.00");
        baseinfo.setPalg_diag_fee("500.00");
        baseinfo.setLab_diag_fee("1500.00");
        baseinfo.setRdhy_diag_fee("800.00");
        baseinfo.setWmfee(new BigDecimal("3500.00"));
        baseinfo.setExam_dspo_matl_fee(new BigDecimal("2000.00"));
        baseinfo.setTrt_dspo_matl_fee(new BigDecimal("1500.00"));
        baseinfo.setOprn_fee(new BigDecimal("8000.00"));
        baseinfo.setAnst_fee(new BigDecimal("1200.00"));
        
        param.setBaseinfo(baseinfo);
        
        // 扩展信息
        Som9001Param.extinfo extinfo = new Som9001Param.extinfo();
        extinfo.setPahca_dia_name("急性心肌梗死");
        extinfo.setPahca_dia_code("I21.900");
        param.setExtinfo(extinfo);
        
        // 诊断信息
        Som9001Param.diseinfo diseinfo = new Som9001Param.diseinfo();
        diseinfo.setMaindiag_flag("1"); // 主诊断
        diseinfo.setDiag_code("I21.900");
        diseinfo.setDiag_name("急性心肌梗死");
        diseinfo.setAdm_cond("2"); // 急症
        diseinfo.setAdm_cond_code("2");
        param.setDiseinfo(diseinfo);
        
        // 手术信息
        Som9001Param.oprninfo oprninfo = new Som9001Param.oprninfo();
        oprninfo.setOprn_oprt_type("1");
        oprninfo.setOprn_oprt_date(new Date());
        oprninfo.setOprn_oprt_name("经皮冠状动脉介入治疗");
        oprninfo.setOprn_oprt_code("36.06");
        oprninfo.setOprn_oprt_sn("1");
        oprninfo.setOprn_lv_code("4"); // 四级手术
        oprninfo.setOper_name("张主任");
        oprninfo.setOper_code("DOC002");
        oprninfo.setAnst_mtd_name("局部麻醉");
        oprninfo.setAnst_mtd_code("2");
        oprninfo.setAnst_dr_name("赵医生");
        oprninfo.setAnst_dr_code("DOC003");
        param.setOprninfo(oprninfo);
        
        // ICU信息
        Som9001Param.icuinfo icuinfo = new Som9001Param.icuinfo();
        icuinfo.setIcu_code("ICU001");
        icuinfo.setInpool_icu_time("2025-08-02 10:00:00");
        icuinfo.setOut_icu_time("2025-08-04 08:00:00");
        param.setIcuinfo(icuinfo);
        
        return param;
    }
    
    /**
     * 生成门诊患者测试数据（DIP分组）
     */
    public static Som9001Param generateOutpatientTestData() {
        Som9001Param param = new Som9001Param();
        
        // 基本信息
        Som9001Param.baseinfo baseinfo = new Som9001Param.baseinfo();
        baseinfo.setFixmedins_code("H12345678901");
        baseinfo.setMedfee_paymtd_code("1"); // 医保支付
        baseinfo.setPatn_ipt_cnt("0"); // 门诊
        baseinfo.setMedcasno("MZ20250812001");
        baseinfo.setPsn_name("李四");
        baseinfo.setGend("2"); // 女性
        baseinfo.setBrdy("1975-03-20");
        baseinfo.setAge("50");
        baseinfo.setCertno("510100197503200002");
        baseinfo.setAdm_way_code("2"); // 门诊
        baseinfo.setAdm_date("2025-08-12 08:30:00");
        baseinfo.setDscg_date("2025-08-12 10:30:00");
        baseinfo.setIpt_days("0"); // 门诊，住院天数为0
        baseinfo.setMedfee_sumamt("3500.00");
        baseinfo.setInsuplc("510100"); // 参保地
        
        // 设置门诊费用
        baseinfo.setOrdn_med_servfee("200.00");
        baseinfo.setPalg_diag_fee("150.00");
        baseinfo.setLab_diag_fee("300.00");
        baseinfo.setWmfee(new BigDecimal("800.00"));
        baseinfo.setExam_dspo_matl_fee(new BigDecimal("500.00"));
        baseinfo.setOprn_fee(new BigDecimal("1500.00")); // 门诊手术费
        
        param.setBaseinfo(baseinfo);
        
        // 扩展信息
        Som9001Param.extinfo extinfo = new Som9001Param.extinfo();
        extinfo.setPahca_dia_name("白内障");
        extinfo.setPahca_dia_code("H25.900");
        param.setExtinfo(extinfo);
        
        // 诊断信息
        Som9001Param.diseinfo diseinfo = new Som9001Param.diseinfo();
        diseinfo.setMaindiag_flag("1"); // 主诊断
        diseinfo.setDiag_code("H25.900");
        diseinfo.setDiag_name("白内障");
        diseinfo.setAdm_cond("1"); // 一般
        diseinfo.setAdm_cond_code("1");
        param.setDiseinfo(diseinfo);
        
        // 手术信息（门诊手术）
        Som9001Param.oprninfo oprninfo = new Som9001Param.oprninfo();
        oprninfo.setOprn_oprt_type("2"); // 门诊手术
        oprninfo.setOprn_oprt_date(new Date());
        oprninfo.setOprn_oprt_name("白内障超声乳化术");
        oprninfo.setOprn_oprt_code("13.41");
        oprninfo.setOprn_oprt_sn("1");
        oprninfo.setOprn_lv_code("2"); // 二级手术
        oprninfo.setOper_name("王医生");
        oprninfo.setOper_code("DOC004");
        oprninfo.setAnst_mtd_name("表面麻醉");
        oprninfo.setAnst_mtd_code("1");
        oprninfo.setAnst_dr_name("李医生");
        oprninfo.setAnst_dr_code("DOC005");
        param.setOprninfo(oprninfo);
        
        // ICU信息（门诊通常不需要）
        Som9001Param.icuinfo icuinfo = new Som9001Param.icuinfo();
        param.setIcuinfo(icuinfo);
        
        return param;
    }
    
    /**
     * 生成胆石症患者测试数据
     */
    public static Som9001Param generateGallstoneTestData() {
        Som9001Param param = new Som9001Param();
        
        // 基本信息
        Som9001Param.baseinfo baseinfo = new Som9001Param.baseinfo();
        baseinfo.setFixmedins_code("H12345678901");
        baseinfo.setMedfee_paymtd_code("1");
        baseinfo.setPatn_ipt_cnt("1");
        baseinfo.setMedcasno("ZY20250812002");
        baseinfo.setPsn_name("王五");
        baseinfo.setGend("1");
        baseinfo.setBrdy("1970-08-10");
        baseinfo.setAge("54");
        baseinfo.setCertno("510100197008100003");
        baseinfo.setAdm_way_code("1");
        baseinfo.setAdm_date("2025-08-10 14:20:00");
        baseinfo.setDscg_date("2025-08-15 10:30:00");
        baseinfo.setIpt_days("5");
        baseinfo.setMedfee_sumamt("18000.00");
        baseinfo.setInsuplc("510100");
        
        // 设置费用
        baseinfo.setOrdn_med_servfee("1000.00");
        baseinfo.setOrdn_trt_oprt_fee("600.00");
        baseinfo.setNurs_fee("400.00");
        baseinfo.setPalg_diag_fee("300.00");
        baseinfo.setLab_diag_fee("800.00");
        baseinfo.setRdhy_diag_fee("600.00");
        baseinfo.setWmfee(new BigDecimal("2500.00"));
        baseinfo.setExam_dspo_matl_fee(new BigDecimal("1500.00"));
        baseinfo.setTrt_dspo_matl_fee(new BigDecimal("1000.00"));
        baseinfo.setOprn_fee(new BigDecimal("6000.00"));
        baseinfo.setAnst_fee(new BigDecimal("800.00"));
        
        param.setBaseinfo(baseinfo);
        
        // 扩展信息
        Som9001Param.extinfo extinfo = new Som9001Param.extinfo();
        extinfo.setPahca_dia_name("胆囊结石");
        extinfo.setPahca_dia_code("K80.200");
        param.setExtinfo(extinfo);
        
        // 诊断信息
        Som9001Param.diseinfo diseinfo = new Som9001Param.diseinfo();
        diseinfo.setMaindiag_flag("1");
        diseinfo.setDiag_code("K80.200");
        diseinfo.setDiag_name("胆囊结石");
        diseinfo.setAdm_cond("2");
        diseinfo.setAdm_cond_code("2");
        param.setDiseinfo(diseinfo);
        
        // 手术信息
        Som9001Param.oprninfo oprninfo = new Som9001Param.oprninfo();
        oprninfo.setOprn_oprt_type("1");
        oprninfo.setOprn_oprt_date(new Date());
        oprninfo.setOprn_oprt_name("腹腔镜胆囊切除术");
        oprninfo.setOprn_oprt_code("51.23");
        oprninfo.setOprn_oprt_sn("1");
        oprninfo.setOprn_lv_code("3");
        oprninfo.setOper_name("陈医生");
        oprninfo.setOper_code("DOC006");
        oprninfo.setAnst_mtd_name("全身麻醉");
        oprninfo.setAnst_mtd_code("3");
        oprninfo.setAnst_dr_name("刘医生");
        oprninfo.setAnst_dr_code("DOC007");
        param.setOprninfo(oprninfo);
        
        // ICU信息
        Som9001Param.icuinfo icuinfo = new Som9001Param.icuinfo();
        param.setIcuinfo(icuinfo);
        
        return param;
    }
}
