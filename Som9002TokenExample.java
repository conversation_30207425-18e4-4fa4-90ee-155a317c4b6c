import com.fasterxml.jackson.databind.ObjectMapper;
import com.my.som.dts.entity.param.CommonParam;
import com.my.som.dts.entity.param.Som9002Param;

/**
 * Som9002 Token生成示例
 * 演示如何使用Som9002Controller生成JWT Token
 */
public class Som9002TokenExample {

    public static void main(String[] args) {
        // 创建示例数据
        Som9002Param som9002Param = new Som9002Param();
        som9002Param.setUsername("admin");

        CommonParam<Som9002Param> param = new CommonParam<>();
        param.setInput(som9002Param);

        try {
            ObjectMapper mapper = new ObjectMapper();
            String jsonRequest = mapper.writeValueAsString(param);
            
            System.out.println("=== Som9002 Token生成请求示例 ===");
            System.out.println("请求URL: POST /dts/generateToken");
            System.out.println("请求头: Content-Type: application/json");
            System.out.println("请求体:");
            System.out.println(jsonRequest);
            
            System.out.println("\n=== 预期响应示例 ===");
            System.out.println("{");
            System.out.println("  \"code\": 200,");
            System.out.println("  \"message\": \"操作成功\",");
            System.out.println("  \"data\": {");
            System.out.println("    \"username\": \"admin\",");
            System.out.println("    \"token\": \"eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImNyZWF0ZWQiOjE2OTQ1...\",");
            System.out.println("    \"tokenType\": \"Bearer\",");
            System.out.println("    \"message\": \"Token生成成功\"");
            System.out.println("  }");
            System.out.println("}");
            
            System.out.println("\n=== 使用Token访问其他接口 ===");
            System.out.println("在后续请求中，需要在请求头中添加:");
            System.out.println("Authorization: Bearer <生成的token>");
            System.out.println("例如:");
            System.out.println("Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImNyZWF0ZWQiOjE2OTQ1...");
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 创建不同用户的测试数据
     */
    public static void createMultipleUserExamples() {
        String[] usernames = {"admin", "doctor", "nurse", "patient"};
        
        System.out.println("=== 多用户Token生成示例 ===");
        
        for (String username : usernames) {
            Som9002Param param = new Som9002Param();
            param.setUsername(username);
            
            CommonParam<Som9002Param> commonParam = new CommonParam<>();
            commonParam.setInput(param);
            
            try {
                ObjectMapper mapper = new ObjectMapper();
                String json = mapper.writeValueAsString(commonParam);
                System.out.println("用户: " + username);
                System.out.println("请求: " + json);
                System.out.println("---");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 错误情况示例
     */
    public static void createErrorExamples() {
        System.out.println("=== 错误情况示例 ===");
        
        // 空用户名
        Som9002Param emptyParam = new Som9002Param();
        emptyParam.setUsername("");
        
        CommonParam<Som9002Param> emptyCommonParam = new CommonParam<>();
        emptyCommonParam.setInput(emptyParam);
        
        // null用户名
        Som9002Param nullParam = new Som9002Param();
        nullParam.setUsername(null);
        
        CommonParam<Som9002Param> nullCommonParam = new CommonParam<>();
        nullCommonParam.setInput(nullParam);
        
        try {
            ObjectMapper mapper = new ObjectMapper();
            
            System.out.println("1. 空用户名请求:");
            System.out.println(mapper.writeValueAsString(emptyCommonParam));
            System.out.println("预期响应: {\"code\": 500, \"message\": \"用户名不能为空\"}");
            System.out.println();
            
            System.out.println("2. null用户名请求:");
            System.out.println(mapper.writeValueAsString(nullCommonParam));
            System.out.println("预期响应: {\"code\": 500, \"message\": \"用户名不能为空\"}");
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
