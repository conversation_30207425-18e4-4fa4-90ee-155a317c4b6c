package com.my.som.dts.entity.vo;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 预分组结果VO
 * 用于返回DRG/DIP预分组结果
 */
@Data
public class PreGroupVo {

    /**
     * DRG分组编码
     */
    private String drgCodg;

    /**
     * DRG分组名称
     */
    private String drgName;

    /**
     * DIP分组编码
     */
    private String dipCodg;

    /**
     * DIP分组名称
     */
    private String dipName;

    /**
     * 分组权重
     */
    private BigDecimal groupWeight;

    /**
     * 预估费用
     */
    private BigDecimal estimatedAmount;

    /**
     * 分组类型 (DRG/DIP)
     */
    private String groupType;

    /**
     * 分组状态
     */
    private String groupStatus;

    /**
     * 分组消息
     */
    private String groupMessage;

    /**
     * 医保支付标准
     */
    private BigDecimal paymentStandard;

    /**
     * 分组版本
     */
    private String groupVersion;

    /**
     * 分组时间
     */
    private String groupTime;

    /**
     * 是否成功分组
     */
    private Boolean isGrouped;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 创建成功的预分组结果
     */
    public static PreGroupVo success(String drgCodg, String drgName, String dipCodg, String dipName) {
        PreGroupVo vo = new PreGroupVo();
        vo.setDrgCodg(drgCodg);
        vo.setDrgName(drgName);
        vo.setDipCodg(dipCodg);
        vo.setDipName(dipName);
        vo.setIsGrouped(true);
        vo.setGroupStatus("SUCCESS");
        return vo;
    }

    /**
     * 创建失败的预分组结果
     */
    public static PreGroupVo failed(String errorMessage) {
        PreGroupVo vo = new PreGroupVo();
        vo.setIsGrouped(false);
        vo.setGroupStatus("FAILED");
        vo.setErrorMessage(errorMessage);
        return vo;
    }
}
