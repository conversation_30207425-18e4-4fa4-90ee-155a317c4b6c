package com.my.som.dts.entity.param;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class Som9003Param {

    private baseinfo baseinfo;

    @Data
    public static class baseinfo {

        private String fixmedins_code;

        private String medcasno;

        private String psn_name;

        private String gend;

        private String age;

        private String nwb_bir_wt;

        private String nwb_adm_wt;

        private Date dscg_date;

        private String ipt_days;

        private String dscg_way;

        private String vent_used_dura;

        private BigDecimal medfee_sumamt;

        private BigDecimal insuplc;

        private BigDecimal icu_care_dura;

        private List<diseinfo> diseinfo;

        private List<oprninfo> oprninfo;
    }

    @Data
    public static class diseinfo {

        private String maindiag_flag;

        private String diag_code;

        private String diag_name;
    }

    @Data
    public static class oprninfo{

        private String oprn_oprt_type;

        private String oprn_oprt_name;

        private String oprn_oprt_code;
    }



}
