package com.my.som.dts.controller;

import com.my.som.common.api.CommonResult;
import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dto.patienInfo.PatienInfo;
import com.my.som.dts.entity.param.CommonParam;
import com.my.som.dts.entity.param.Som9001Param;
import com.my.som.service.medicalQuality.SettleListManageService;
import com.my.som.util.GroupCommonUtil;
import com.my.som.util.SysCommonConfigUtil;
import com.my.som.vo.pregroup.PreGroupVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/dts")
public class Som9001Controller {

    @Resource
    private SettleListManageService settleListManageService;

    @RequestMapping("/Som9001")
    public String Som9001(@RequestBody CommonParam<Som9001Param> param) {
        return "Som9001";
    }

    @ApiOperation("查询模拟预分组结果")
    @PostMapping("/getPreGroupResult")
    @ResponseBody
    public CommonResult preGroupResult(@RequestBody CommonParam<Som9001Param> param, HttpServletRequest request, HttpServletResponse response) {
        Som9001Param som9001Param = param.getInput();

        // 将Som9001Param转换为PatienInfo
        PatienInfo patienInfo = convertToPatienInfo(som9001Param);

        // 处理参保地信息
        handleInsuplc(patienInfo);

        // 调用预分组服务
        PreGroupVo groups = settleListManageService.getSimlatePreGroup(patienInfo, request, response);

        if (groups.getDrgCodg() != null || groups.getDrgCodg() != "" || groups.getDipCodg() != null || groups.getDipCodg() != "")
            return CommonResult.success(groups);
        else
            return CommonResult.failed("入组失败,请检查输入信息");
    }

    /**
     * 将Som9001Param转换为PatienInfo
     */
    private PatienInfo convertToPatienInfo(Som9001Param som9001Param) {
        PatienInfo patienInfo = new PatienInfo();

        if (som9001Param.getBaseinfo() != null) {
            Som9001Param.baseinfo baseinfo = som9001Param.getBaseinfo();

            // 基本信息映射
            patienInfo.setAge(baseinfo.getAge());
            patienInfo.setSex(baseinfo.getGend());
            patienInfo.setInsuplc(baseinfo.getInsuplc());
            patienInfo.setHosId(baseinfo.getFixmedins_code());

            // 住院天数
            if (StringUtils.hasText(baseinfo.getIpt_days())) {
                try {
                    patienInfo.setHosDays(new BigDecimal(baseinfo.getIpt_days()));
                    patienInfo.setDays(baseinfo.getIpt_days());
                } catch (NumberFormatException e) {
                    patienInfo.setHosDays(BigDecimal.ZERO);
                    patienInfo.setDays("0");
                }
            }

            // 住院费用
            if (StringUtils.hasText(baseinfo.getMedfee_sumamt())) {
                try {
                    patienInfo.setZyFee(new BigDecimal(baseinfo.getMedfee_sumamt()));
                } catch (NumberFormatException e) {
                    patienInfo.setZyFee(BigDecimal.ZERO);
                }
            }

            // 入院方式
            patienInfo.setLyfs(baseinfo.getAdm_way_code());

            // 参保类型
            patienInfo.setCanBaoType(baseinfo.getMedfee_paymtd_code());
        }

        // ICU信息处理
        if (som9001Param.getIcuinfo() != null) {
            Som9001Param.icuinfo icuinfo = som9001Param.getIcuinfo();
            // 这里可以根据需要计算ICU小时数
            // patienInfo.setIcuHours(calculateIcuHours(icuinfo));
        }

        // 诊断信息处理
        List<String> diagCodes = new ArrayList<>();
        if (som9001Param.getDiseinfo() != null) {
            Som9001Param.diseinfo diseinfo = som9001Param.getDiseinfo();
            if (StringUtils.hasText(diseinfo.getDiag_code())) {
                diagCodes.add(diseinfo.getDiag_code());
            }
        }
        patienInfo.setDiagCodes(diagCodes);

        // 手术信息处理
        List<String> oprnsCode = new ArrayList<>();
        if (som9001Param.getOprninfo() != null) {
            Som9001Param.oprninfo oprninfo = som9001Param.getOprninfo();
            if (StringUtils.hasText(oprninfo.getOprn_oprt_code())) {
                oprnsCode.add(oprninfo.getOprn_oprt_code());
            }
        }
        patienInfo.setOprnsCode(oprnsCode);

        return patienInfo;
    }

    /**
     * 处理参保地信息
     */
    private void handleInsuplc(PatienInfo patienInfo) {
        String provLevelInsuplcAdmdvs =
                ValidateUtil.isEmpty(GroupCommonUtil.getInsuplcAdmdvs()) ? "0000" : GroupCommonUtil.getInsuplcAdmdvs();
        String insuplc = patienInfo.getInsuplc();
        String insuPlaceType = (String) SysCommonConfigUtil.get(DrgConst.INSUPLC_ADMDVS_AREA);

        if (insuplc != null) {
            switch (insuplc) {
                case "0":
                    insuplc = insuPlaceType;
                    break;
                case "1":
                    insuplc = insuPlaceType.substring(0, 2) + "00";
                    break;
                case "2":
                    insuplc = "0000";
                    break;
                case "3":
                    insuplc = provLevelInsuplcAdmdvs;
                    break;
            }
            patienInfo.setInsuplc(insuplc);
        }
    }
}
