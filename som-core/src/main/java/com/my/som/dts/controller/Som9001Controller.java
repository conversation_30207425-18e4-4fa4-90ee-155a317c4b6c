package com.my.som.dts.controller;

import com.my.som.common.api.CommonResult;
import com.my.som.dts.entity.param.CommonParam;
import com.my.som.dts.entity.param.Som9001Param;
import com.my.som.dts.entity.vo.PreGroupVo;
import com.my.som.service.medicalQuality.SettleListManageService;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/dts")
public class Som9001Controller {

    @Resource
    private SettleListManageService SettleListManageService;

    @RequestMapping("/Som9001")
    public String Som9001(@RequestBody CommonParam<Som9001Param> param) {
        return "Som9001";
    }



}
