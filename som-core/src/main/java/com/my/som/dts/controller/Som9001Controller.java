package com.my.som.dts.controller;

import com.my.som.common.api.CommonResult;
import com.my.som.dto.patienInfo.PatienInfo;
import com.my.som.dts.entity.param.CommonParam;
import com.my.som.dts.entity.param.Som9001Param;
import com.my.som.dts.entity.vo.PreGroupVo;
import com.my.som.service.medicalQuality.SettleListManageService;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/dts")
public class Som9001Controller {

    @Resource
    private SettleListManageService settleListManageService;

    @RequestMapping("/Som9001")
    public CommonResult Som9001(@RequestBody CommonParam<Som9001Param> param) {
        Som9001Param som9001Param = param.getInput();
        PatienInfo patienInfo = convertToPatienInfo(som9001Param);
        // 处理参保地信息
        handleInsuplc(patienInfo);

        // 调用预分组服务
        PreGroupVo groups = settleListManageService.getSimlatePreGroup(patienInfo, request, response);

        if (groups.getDrgCodg() != null || groups.getDrgCodg() != "" || groups.getDipCodg() != null || groups.getDipCodg() != "")
            return CommonResult.success(groups);
        else
            return CommonResult.failed("入组失败,请检查输入信息");
    }



}
