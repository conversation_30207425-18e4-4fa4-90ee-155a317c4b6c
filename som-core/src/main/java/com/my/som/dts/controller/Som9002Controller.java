package com.my.som.dts.controller;

import com.my.som.bo.SysUserDetails;
import com.my.som.common.api.CommonResult;
import com.my.som.common.vo.SomBackUser;
import com.my.som.dts.entity.param.CommonParam;
import com.my.som.dts.entity.param.Som9002Param;
import com.my.som.security.util.JwtTokenUtil;
import com.my.som.vo.SomMenuMgt;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/dts")
public class Som9002Controller {

    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    @RequestMapping("/Som9002")
    public String Som9002(@RequestBody CommonParam<Som9002Param> param) {
        return "Som9002";
    }

    @ApiOperation("根据用户名生成Token")
    @PostMapping("/generateToken")
    @ResponseBody
    public CommonResult generateToken(@RequestBody CommonParam<Som9002Param> param) {
        Som9002Param input = param.getInput();
        String username = input.getUsername();

        // 验证用户名是否为空
        if (!StringUtils.hasText(username)) {
            return CommonResult.failed("用户名不能为空");
        }

        // 创建用户详情对象（模拟用户信息）
        SysUserDetails userDetails = createMockUserDetails(username);

        // 生成JWT token
        String token = jwtTokenUtil.generateToken(userDetails);

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("username", username);
        result.put("token", token);
        result.put("tokenType", "Bearer");
        result.put("message", "Token生成成功");

        return CommonResult.success(result);
    }

    /**
     * 创建模拟用户详情对象
     * @param username 用户名
     * @return SysUserDetails对象
     */
    private SysUserDetails createMockUserDetails(String username) {
        // 创建SomBackUser对象
        SomBackUser somBackUser = new SomBackUser();
        somBackUser.setUsername(username);
        somBackUser.setPassword(""); // 空密码，仅用于token生成
        somBackUser.setStatus(1); // 启用状态
        somBackUser.setNknm(username); // 昵称设置为用户名
        somBackUser.setIsLckUser((short) 0); // 未锁定

        // 创建权限列表（空列表，因为只是用于token生成）
        List<SomMenuMgt> permissionList = new ArrayList<>();

        // 创建SysUserDetails对象
        SysUserDetails userDetails = new SysUserDetails(somBackUser, permissionList);

        return userDetails;
    }
}
