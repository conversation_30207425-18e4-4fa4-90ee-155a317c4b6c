package com.my.som.dts.controller;

import com.my.som.service.itft.ITFTUploadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Map;

@RestController
@RequestMapping("/dts")
public class Som4101Controller {

    @Resource
    private ITFTUploadService itftUploadService;


    @PostMapping(value = "/Som4101", consumes = {MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> processData(@RequestBody Map input, HttpServletRequest request) throws Exception {
        ArrayList<Map> maps = new ArrayList<>();
        maps.add((Map) input.get("input"));

        itftUploadService.inertDi(maps);
        return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_JSON)
                    .body("成功");

    }
}