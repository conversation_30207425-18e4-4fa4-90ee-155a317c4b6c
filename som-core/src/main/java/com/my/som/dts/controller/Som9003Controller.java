package com.my.som.dts.controller;

import com.my.som.common.api.CommonResult;
import com.my.som.dts.entity.param.CommonParam;
import com.my.som.dts.entity.param.Som9003Param;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/dts")
public class Som9003Controller {

    @RequestMapping("/Som9003")
    public CommonResult Som9003(@RequestBody CommonParam<Som9003Param> param){

    }

}
