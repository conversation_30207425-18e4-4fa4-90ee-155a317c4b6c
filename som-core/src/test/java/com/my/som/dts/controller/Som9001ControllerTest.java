package com.my.som.dts.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.my.som.dts.entity.param.CommonParam;
import com.my.som.dts.entity.param.Som9001Param;
import com.my.som.service.medicalQuality.SettleListManageService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.util.Date;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Som9001Controller测试类
 */
@WebMvcTest(Som9001Controller.class)
public class Som9001ControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private SettleListManageService settleListManageService;

    @Autowired
    private ObjectMapper objectMapper;

    private CommonParam<Som9001Param> testParam;

    @BeforeEach
    void setUp() {
        testParam = createTestParam();
    }

    @Test
    void testPreGroupResult() throws Exception {
        String jsonContent = objectMapper.writeValueAsString(testParam);

        mockMvc.perform(post("/dts/getPreGroupResult")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonContent))
                .andExpect(status().isOk());
    }

    /**
     * 创建测试数据
     */
    private CommonParam<Som9001Param> createTestParam() {
        CommonParam<Som9001Param> param = new CommonParam<>();
        Som9001Param som9001Param = new Som9001Param();

        // 基本信息
        Som9001Param.baseinfo baseinfo = new Som9001Param.baseinfo();
        baseinfo.setFixmedins_code("H12345678901");
        baseinfo.setMedfee_paymtd_code("1"); // 医保支付
        baseinfo.setPatn_ipt_cnt("1");
        baseinfo.setMedcasno("ZY20250812001");
        baseinfo.setPsn_name("张三");
        baseinfo.setGend("1"); // 男性
        baseinfo.setBrdy("1980-05-15");
        baseinfo.setAge("44");
        baseinfo.setCertno("510100198005150001");
        baseinfo.setAdm_way_code("1"); // 急诊
        baseinfo.setAdm_date("2025-08-01 09:30:00");
        baseinfo.setDscg_date("2025-08-07 15:20:00");
        baseinfo.setIpt_days("6"); // 住院6天
        baseinfo.setMedfee_sumamt("25000.50");
        baseinfo.setInsuplc("510100"); // 参保地

        // 设置各项费用
        baseinfo.setOrdn_med_servfee("1200.00");
        baseinfo.setOrdn_trt_oprt_fee("800.00");
        baseinfo.setNurs_fee("600.00");
        baseinfo.setPalg_diag_fee("500.00");
        baseinfo.setLab_diag_fee("1500.00");
        baseinfo.setRdhy_diag_fee("800.00");
        baseinfo.setWmfee(new BigDecimal("3500.00"));
        baseinfo.setExam_dspo_matl_fee(new BigDecimal("2000.00"));
        baseinfo.setTrt_dspo_matl_fee(new BigDecimal("1500.00"));
        baseinfo.setOprn_fee(new BigDecimal("8000.00"));
        baseinfo.setAnst_fee(new BigDecimal("1200.00"));

        som9001Param.setBaseinfo(baseinfo);

        // 扩展信息
        Som9001Param.extinfo extinfo = new Som9001Param.extinfo();
        extinfo.setPahca_dia_name("急性心肌梗死");
        extinfo.setPahca_dia_code("I21.900");
        som9001Param.setExtinfo(extinfo);

        // 诊断信息
        Som9001Param.diseinfo diseinfo = new Som9001Param.diseinfo();
        diseinfo.setMaindiag_flag("1"); // 主诊断
        diseinfo.setDiag_code("I21.900");
        diseinfo.setDiag_name("急性心肌梗死");
        diseinfo.setAdm_cond("2"); // 急症
        diseinfo.setAdm_cond_code("2");
        som9001Param.setDiseinfo(diseinfo);

        // 手术信息
        Som9001Param.oprninfo oprninfo = new Som9001Param.oprninfo();
        oprninfo.setOprn_oprt_type("1");
        oprninfo.setOprn_oprt_date(new Date());
        oprninfo.setOprn_oprt_name("经皮冠状动脉介入治疗");
        oprninfo.setOprn_oprt_code("36.06");
        oprninfo.setOprn_oprt_sn("1");
        oprninfo.setOprn_lv_code("4"); // 四级手术
        oprninfo.setOper_name("张主任");
        oprninfo.setOper_code("DOC002");
        oprninfo.setAnst_mtd_name("局部麻醉");
        oprninfo.setAnst_mtd_code("2");
        oprninfo.setAnst_dr_name("赵医生");
        oprninfo.setAnst_dr_code("DOC003");
        som9001Param.setOprninfo(oprninfo);

        // ICU信息
        Som9001Param.icuinfo icuinfo = new Som9001Param.icuinfo();
        icuinfo.setIcu_code("ICU001");
        icuinfo.setInpool_icu_time("2025-08-02 10:00:00");
        icuinfo.setOut_icu_time("2025-08-04 08:00:00");
        som9001Param.setIcuinfo(icuinfo);

        param.setInput(som9001Param);
        return param;
    }
}
