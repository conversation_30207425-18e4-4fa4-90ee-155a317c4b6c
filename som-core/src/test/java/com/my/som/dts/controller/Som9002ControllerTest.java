package com.my.som.dts.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.my.som.bo.SysUserDetails;
import com.my.som.dts.entity.param.CommonParam;
import com.my.som.dts.entity.param.Som9002Param;
import com.my.som.security.util.JwtTokenUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.ArgumentMatchers.any;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Som9002Controller测试类
 */
@WebMvcTest(Som9002Controller.class)
public class Som9002ControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private JwtTokenUtil jwtTokenUtil;

    @Autowired
    private ObjectMapper objectMapper;

    private CommonParam<Som9002Param> testParam;

    @BeforeEach
    void setUp() {
        testParam = createTestParam();
        // Mock JwtTokenUtil to return a test token
        Mockito.when(jwtTokenUtil.generateToken(any(SysUserDetails.class)))
                .thenReturn("test-jwt-token-12345");
    }

    @Test
    void testGenerateToken() throws Exception {
        String jsonContent = objectMapper.writeValueAsString(testParam);

        mockMvc.perform(post("/dts/generateToken")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonContent))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.username").value("testuser"))
                .andExpect(jsonPath("$.data.token").value("test-jwt-token-12345"))
                .andExpect(jsonPath("$.data.tokenType").value("Bearer"));
    }

    @Test
    void testGenerateTokenWithEmptyUsername() throws Exception {
        Som9002Param emptyParam = new Som9002Param();
        emptyParam.setUsername("");
        
        CommonParam<Som9002Param> param = new CommonParam<>();
        param.setInput(emptyParam);
        
        String jsonContent = objectMapper.writeValueAsString(param);

        mockMvc.perform(post("/dts/generateToken")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonContent))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").value("用户名不能为空"));
    }

    @Test
    void testGenerateTokenWithNullUsername() throws Exception {
        Som9002Param nullParam = new Som9002Param();
        nullParam.setUsername(null);
        
        CommonParam<Som9002Param> param = new CommonParam<>();
        param.setInput(nullParam);
        
        String jsonContent = objectMapper.writeValueAsString(param);

        mockMvc.perform(post("/dts/generateToken")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonContent))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").value("用户名不能为空"));
    }

    /**
     * 创建测试数据
     */
    private CommonParam<Som9002Param> createTestParam() {
        CommonParam<Som9002Param> param = new CommonParam<>();
        Som9002Param som9002Param = new Som9002Param();
        som9002Param.setUsername("testuser");
        param.setInput(som9002Param);
        return param;
    }
}
